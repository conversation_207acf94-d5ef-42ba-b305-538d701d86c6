#!/usr/bin/env node

// 测试 XML 解析功能
const BuildFixer = require('./src/build/buildFixer');

// 创建一个测试实例
const buildFixer = new BuildFixer('/test/path', {});

// 测试 XML 格式的 AI 响应
const xmlResponse = `
这是一个修复建议：

<file_edit>
<path>src/main.js</path>
<content>
import { createApp } from 'vue'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import App from './App.vue'

const app = createApp(App)
app.use(ElementPlus)
app.mount('#app')
</content>
</file_edit>

修复完成。
`;

// 测试代码块格式的 AI 响应
const codeBlockResponse = `
这是修复后的代码：

\`\`\`javascript
import { createApp } from 'vue'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import App from './App.vue'

const app = createApp(App)
app.use(ElementPlus)
app.mount('#app')
\`\`\`

请使用这个修复后的代码。
`;

// 测试普通文本响应
const plainResponse = `
import { createApp } from 'vue'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import App from './App.vue'

const app = createApp(App)
app.use(ElementPlus)
app.mount('#app')
`;

const originalContent = `
import Vue from 'vue'
import ElementUI from 'element-ui'
import 'element-ui/lib/theme-chalk/index.css'

Vue.use(ElementUI)

new Vue({
  el: '#app',
  render: h => h(App)
})
`;

console.log('=== 测试 XML 格式解析 ===');
const xmlResult = buildFixer.parseAIResponse(xmlResponse, originalContent);
console.log('解析结果:');
console.log(xmlResult);

console.log('\n=== 测试代码块格式解析 ===');
const codeBlockResult = buildFixer.parseAIResponse(codeBlockResponse, originalContent);
console.log('解析结果:');
console.log(codeBlockResult);

console.log('\n=== 测试普通文本解析 ===');
const plainResult = buildFixer.parseAIResponse(plainResponse, originalContent);
console.log('解析结果:');
console.log(plainResult);
