const fs = require('fs-extra');
const path = require('path');
const chalk = require('chalk');
const glob = require('glob');
const jscodeshift = require('jscodeshift');

// 导入各个 codemod 转换器
const migrateLibraryTargetTransform = require('./codemods/migrateLibraryTargetToLibraryObject');
const setTargetToFalseTransform = require('./codemods/setTargetToFalseAndUpdatePlugins');
const jsonImportsTransform = require('./codemods/jsonImportsToDefaultImports');

/**
 * Webpack Codemod 迁移器
 * 基于官方 webpack v5 codemod 实现，提供 API 方式的代码转换
 * 
 * 支持的转换：
 * - migrate-library-target-to-library-object
 * - set-target-to-false-and-update-plugins
 * - json-imports-to-default-imports
 */
class WebpackCodemodMigrator {
  constructor(projectPath, options = {}) {
    this.projectPath = path.resolve(projectPath);
    this.options = {
      dryRun: false,
      verbose: false,
      backup: true,
      include: ['**/*.js', '**/*.jsx', '**/*.ts', '**/*.tsx'],
      exclude: ['node_modules/**', 'dist/**', 'build/**', '**/*.min.js'],
      parser: 'tsx', // 支持 TypeScript 和 JSX
      ...options
    };

    this.stats = {
      totalFiles: 0,
      processedFiles: 0,
      modifiedFiles: 0,
      skippedFiles: 0,
      errors: [],
      transformations: {
        libraryTarget: 0,
        targetToFalse: 0,
        jsonImports: 0
      }
    };

    // 初始化 jscodeshift
    this.jscodeshift = jscodeshift.withParser(this.options.parser);
  }

  /**
   * 执行完整的 webpack v5 迁移
   */
  async migrate() {
    console.log(chalk.bold.blue('\n🔧 Webpack v5 Codemod 迁移器'));
    console.log(chalk.gray(`项目路径: ${this.projectPath}`));

    try {
      // 查找需要处理的文件
      const files = await this.findTargetFiles();
      this.stats.totalFiles = files.length;

      if (files.length === 0) {
        console.log(chalk.yellow('⚠️  未找到需要处理的文件'));
        return this.createResult(true, '未找到需要处理的文件');
      }

      console.log(chalk.gray(`找到 ${files.length} 个文件需要处理`));

      // 执行各个转换
      await this.runTransformations(files);

      // 输出统计信息
      this.printStats();

      const success = this.stats.errors.length === 0;
      const message = success 
        ? `成功处理 ${this.stats.modifiedFiles} 个文件`
        : `处理完成，但有 ${this.stats.errors.length} 个错误`;

      return this.createResult(success, message);

    } catch (error) {
      console.error(chalk.red('❌ 迁移过程中发生错误:'), error.message);
      return this.createResult(false, error.message);
    }
  }

  /**
   * 查找目标文件
   */
  async findTargetFiles() {
    const allFiles = [];

    for (const pattern of this.options.include) {
      const files = glob.sync(pattern, {
        cwd: this.projectPath,
        absolute: true,
        ignore: this.options.exclude
      });
      allFiles.push(...files);
    }

    // 去重并过滤存在的文件
    const uniqueFiles = [...new Set(allFiles)];
    const existingFiles = [];

    for (const file of uniqueFiles) {
      if (await fs.pathExists(file)) {
        existingFiles.push(file);
      }
    }

    return existingFiles;
  }

  /**
   * 执行所有转换
   */
  async runTransformations(files) {
    const transformations = [
      {
        name: 'migrate-library-target-to-library-object',
        transform: migrateLibraryTargetTransform,
        description: '迁移 library target 到 library object'
      },
      {
        name: 'set-target-to-false-and-update-plugins',
        transform: setTargetToFalseTransform,
        description: '设置 target 为 false 并更新 plugins'
      },
      {
        name: 'json-imports-to-default-imports',
        transform: jsonImportsTransform,
        description: '转换 JSON 命名导入为默认导入'
      }
    ];

    for (const { name, transform, description } of transformations) {
      console.log(chalk.blue(`\n📝 执行转换: ${description}`));
      await this.runSingleTransformation(files, name, transform);
    }
  }

  /**
   * 执行单个转换
   */
  async runSingleTransformation(files, transformName, transformFunction) {
    let transformedCount = 0;

    for (const filePath of files) {
      try {
        const result = await this.transformFile(filePath, transformFunction);
        
        if (result.modified) {
          transformedCount++;
          this.stats.transformations[this.getTransformKey(transformName)]++;
          
          if (this.options.verbose) {
            console.log(chalk.green(`    ✅ ${path.relative(this.projectPath, filePath)}`));
          }
        }

      } catch (error) {
        this.stats.errors.push({
          file: filePath,
          transform: transformName,
          error: error.message
        });

        if (this.options.verbose) {
          console.log(chalk.red(`    ❌ ${path.relative(this.projectPath, filePath)}: ${error.message}`));
        }
      }
    }

    if (transformedCount > 0) {
      console.log(chalk.green(`    转换了 ${transformedCount} 个文件`));
    } else {
      console.log(chalk.gray('    没有文件需要转换'));
    }
  }

  /**
   * 转换单个文件
   */
  async transformFile(filePath, transformFunction) {
    const source = await fs.readFile(filePath, 'utf8');
    
    // 执行转换
    const result = transformFunction(
      { path: filePath, source },
      { jscodeshift: this.jscodeshift },
      {}
    );

    // 检查是否有修改
    if (result && result !== source) {
      this.stats.processedFiles++;
      
      if (!this.options.dryRun) {
        // 创建备份
        if (this.options.backup) {
          await this.createBackup(filePath);
        }

        // 写入修改后的内容
        await fs.writeFile(filePath, result, 'utf8');
        this.stats.modifiedFiles++;
      }

      return { modified: true, result };
    }

    return { modified: false };
  }

  /**
   * 创建文件备份
   */
  async createBackup(filePath) {
    const backupPath = `${filePath}.backup`;
    await fs.copy(filePath, backupPath);
  }

  /**
   * 获取转换统计键名
   */
  getTransformKey(transformName) {
    const keyMap = {
      'migrate-library-target-to-library-object': 'libraryTarget',
      'set-target-to-false-and-update-plugins': 'targetToFalse',
      'json-imports-to-default-imports': 'jsonImports'
    };
    return keyMap[transformName] || 'unknown';
  }

  /**
   * 打印统计信息
   */
  printStats() {
    console.log(chalk.bold.blue('\n📊 迁移统计'));
    console.log(chalk.gray(`总文件数: ${this.stats.totalFiles}`));
    console.log(chalk.gray(`处理文件数: ${this.stats.processedFiles}`));
    console.log(chalk.gray(`修改文件数: ${this.stats.modifiedFiles}`));
    console.log(chalk.gray(`跳过文件数: ${this.stats.skippedFiles}`));

    console.log(chalk.bold.blue('\n🔄 转换统计'));
    console.log(chalk.gray(`Library Target 转换: ${this.stats.transformations.libraryTarget}`));
    console.log(chalk.gray(`Target to False 转换: ${this.stats.transformations.targetToFalse}`));
    console.log(chalk.gray(`JSON Imports 转换: ${this.stats.transformations.jsonImports}`));

    if (this.stats.errors.length > 0) {
      console.log(chalk.bold.red('\n❌ 错误列表'));
      this.stats.errors.forEach(error => {
        console.log(chalk.red(`  ${path.relative(this.projectPath, error.file)}: ${error.error}`));
      });
    }
  }

  /**
   * 创建结果对象
   */
  createResult(success, message) {
    return {
      success,
      message,
      stats: this.stats
    };
  }
}

module.exports = WebpackCodemodMigrator;
