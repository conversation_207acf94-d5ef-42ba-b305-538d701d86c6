/**
 * 配置文件修复器
 * 用于自动修复各种配置文件中的兼容性问题
 */

const fs = require('fs-extra')
const path = require('path')
const chalk = require('chalk')

class ConfigFixer {
	constructor(projectPath) {
		this.projectPath = projectPath
	}

	/**
	 * 修复配置文件
	 */
	async fixConfigFiles(suggestions) {
		const fixedFiles = []

		for (const suggestion of suggestions) {
			try {
				const fixed = await this.fixConfigFile(suggestion)
				if (fixed) {
					fixedFiles.push(suggestion.file)
					console.log(chalk.green(`  ✅ 已自动修复 ${suggestion.file}`))
				}
			} catch (error) {
				console.warn(chalk.yellow(`  ⚠️  无法自动修复 ${suggestion.file}: ${error.message}`))
			}
		}

		return fixedFiles
	}

	/**
	 * 修复单个配置文件
	 */
	async fixConfigFile(suggestion) {
		const configPath = path.join(this.projectPath, suggestion.file)
		
		if (!await fs.pathExists(configPath)) {
			return false
		}

		switch (suggestion.file) {
			case 'vue.config.js':
				return await this.fixVueConfig(configPath, suggestion)
			case 'webpack.config.js':
				return await this.fixWebpackConfig(configPath, suggestion)
			case 'vite.config.js':
				return await this.fixViteConfig(configPath, suggestion)
			default:
				return await this.fixGenericConfig(configPath, suggestion)
		}
	}

	/**
	 * 修复 vue.config.js
	 */
	async fixVueConfig(configPath, suggestion) {
		let content = await fs.readFile(configPath, 'utf8')
		let modified = false

		if (suggestion.patterns) {
			for (const pattern of suggestion.patterns) {
				if (pattern.pattern.test(content)) {
					content = content.replace(pattern.pattern, '')
					modified = true
					console.log(chalk.gray(`    - 已移除 ${pattern.type} 中的 ${suggestion.package} 配置`))
				}
			}
		}

		// 清理代码
		if (modified) {
			content = this.cleanupCode(content)
		}

		if (modified) {
			await fs.writeFile(configPath, content, 'utf8')
			return true
		}

		return false
	}

	/**
	 * 修复 webpack.config.js
	 */
	async fixWebpackConfig(configPath, suggestion) {
		// 类似 vue.config.js 的处理逻辑
		return await this.fixVueConfig(configPath, suggestion)
	}

	/**
	 * 修复 vite.config.js
	 */
	async fixViteConfig(configPath, suggestion) {
		// Vite 配置的特殊处理逻辑
		let content = await fs.readFile(configPath, 'utf8')
		let modified = false

		// 处理 Vite 特定的配置问题
		if (suggestion.package === 'svg-sprite-loader') {
			// 在 Vite 中，svg-sprite-loader 通常被替换为 vite-plugin-svg-icons
			const svgLoaderPattern = /(\s*svgSpriteLoader\s*:\s*\{[^}]*\}\s*,?\s*)/g
			if (svgLoaderPattern.test(content)) {
				content = content.replace(svgLoaderPattern, '')
				modified = true
				console.log(chalk.gray(`    - 已移除 Vite 配置中的 svg-sprite-loader 配置`))
			}
		}

		if (modified) {
			content = this.cleanupCode(content)
			await fs.writeFile(configPath, content, 'utf8')
			return true
		}

		return false
	}

	/**
	 * 修复通用配置文件
	 */
	async fixGenericConfig(configPath, suggestion) {
		// 通用配置文件修复逻辑
		return await this.fixVueConfig(configPath, suggestion)
	}

	/**
	 * 清理代码
	 */
	cleanupCode(content) {
		// 清理空数组
		content = content.replace(/plugins:\s*\[\s*\]/g, 'plugins: []')
		content = content.replace(/loaders:\s*\[\s*\]/g, 'loaders: []')
		content = content.replace(/rules:\s*\[\s*\]/g, 'rules: []')
		
		// 清理多余的逗号
		content = content.replace(/,\s*,/g, ',')
		content = content.replace(/,\s*}/g, '}')
		content = content.replace(/,\s*]/g, ']')
		content = content.replace(/,\s*\)/g, ')')
		
		// 清理多余的空行
		content = content.replace(/\n\s*\n\s*\n/g, '\n\n')
		
		return content
	}

	/**
	 * 生成修复建议
	 */
	generateFixSuggestions(suggestions) {
		const fixSuggestions = []

		for (const suggestion of suggestions) {
			fixSuggestions.push({
				file: suggestion.file,
				action: suggestion.action,
				description: suggestion.description,
				package: suggestion.package,
				error: suggestion.error,
				solution: suggestion.solution,
				manualSteps: this.generateManualSteps(suggestion)
			})
		}

		return fixSuggestions
	}

	/**
	 * 生成手动修复步骤
	 */
	generateManualSteps(suggestion) {
		const steps = []

		switch (suggestion.package) {
			case 'script-ext-html-webpack-plugin':
				steps.push('1. 在 vue.config.js 中移除 ScriptExtHtmlWebpackPlugin 的 require 语句')
				steps.push('2. 移除 chainWebpack 中的 ScriptExtHtmlWebpackPlugin 配置')
				steps.push('3. 移除 configureWebpack 中的 ScriptExtHtmlWebpackPlugin 实例')
				break
			case 'svg-sprite-loader':
				steps.push('1. 卸载旧版本: npm uninstall svg-sprite-loader')
				steps.push('2. 安装新版本: npm install -D svg-sprite-loader@6.0.11')
				steps.push('3. 如果使用 Vite，考虑迁移到 vite-plugin-svg-icons')
				break
			default:
				steps.push(`1. 检查 ${suggestion.file} 中的 ${suggestion.package} 相关配置`)
				steps.push(`2. 根据错误信息进行相应修改`)
		}

		return steps
	}
}

module.exports = ConfigFixer 