const fs = require('fs-extra')
const path = require('path')
const chalk = require('chalk')
const { CompatibilityRuleManager } = require('./compatibilityRules')
const ConfigFixer = require('./configFixer')

class PackageUpgrader {
	constructor (projectPath, options = {}) {
		this.projectPath = projectPath
		this.packageJsonPath = path.join(projectPath, 'package.json')
		this.configPath = path.join(__dirname, '../../config/package-recommend.json')
		this.config = null
		this.options = {
			migrationMode: false, // 是否为迁移模式（从 Vue 2 到 Vue 3）
			preserveVue3Dependencies: true, // 是否保留已有的 Vue 3 依赖
			autoFixConfig: true, // 是否自动修复配置文件
			...options
		}
		this.isVue3Project = false
		
		// 初始化规则引擎和配置修复器
		this.ruleManager = new CompatibilityRuleManager()
		this.configFixer = new ConfigFixer(projectPath)
	}

	/**
	 * 加载配置文件
	 */
	async loadConfig() {
		try {
			this.config = await fs.readJson(this.configPath)
			// 检测项目是否已经是 Vue 3 项目
			await this.detectVueVersion()
		} catch (error) {
			console.error(chalk.red('❌ 无法加载配置文件:'), error.message)
			throw new Error(`Failed to load config file: ${this.configPath}`)
		}
	}

	/**
	 * 检测项目的 Vue 版本
	 */
	async detectVueVersion() {
		try {
			const packageJson = await fs.readJson(this.packageJsonPath)
			const allDeps = {
				...packageJson.dependencies,
				...packageJson.devDependencies
			}

			const vueVersion = allDeps.vue
			if (vueVersion) {
				// 检查是否为 Vue 3 版本
				this.isVue3Project = vueVersion.startsWith('3.') || vueVersion.startsWith('^3.') || vueVersion.startsWith('~3.')
				
				if (this.isVue3Project && this.options.preserveVue3Dependencies) {
					console.log(chalk.yellow('⚠️  检测到 Vue 3 项目，将保留现有兼容依赖版本'))
				}
			}
		} catch (error) {
			console.warn(chalk.yellow('⚠️  无法检测 Vue 版本，将按 Vue 2 项目处理'))
		}
	}

	/**
	 * 获取依赖映射表
	 */
	getDependencyMapping() {
		const mapping = {}
		// 从已知兼容包中获取版本信息
		Object.entries(this.config.knownCompatible).forEach(([name, info]) => {
			if (info.version) {
				mapping[name] = info.version
			}
		})

		// 从需要升级的包中获取版本信息
		Object.entries(this.config.needsUpgrade).forEach(([name, info]) => {
			if (info.version) {
				mapping[name] = info.version
			}
		})

		return mapping
	}

	/**
	 * 获取需要删除的依赖列表
	 */
	getDependenciesToRemove() {
		return Object.keys(this.config.knownIncompatible)
	}

	/**
	 * 获取关键依赖列表（需要优先处理的依赖）
	 */
	getCriticalDependencies() {
		const criticalDeps = []
		Object.entries(this.config.needsUpgrade).forEach(([name, info]) => {
			if (info.critical) {
				criticalDeps.push({ name, ...info })
			}
		})
		return criticalDeps
	}

	/**
	 * 检查是否存在版本兼容性问题
	 */
	async checkVersionCompatibility() {
		try {
			const packageJson = await fs.readJson(this.packageJsonPath)
			const allDeps = {
				...packageJson.dependencies,
				...packageJson.devDependencies
			}

			// 使用规则引擎检查兼容性
			return this.ruleManager.checkCompatibility(allDeps)
		} catch (error) {
			console.warn(chalk.yellow('⚠️  无法检查版本兼容性:'), error.message)
			return []
		}
	}

	/**
	 * 处理版本兼容性问题
	 */
	async handleVersionCompatibility() {
		const issues = await this.checkVersionCompatibility()
		
		if (issues.length === 0) {
			return { handled: false, issues: [] }
		}

		console.log(chalk.yellow('\n⚠️  检测到版本兼容性问题:'))
		
		issues.forEach(issue => {
			console.log(chalk.red(`  ❌ ${issue.package}: ${issue.error}`))
			console.log(chalk.blue(`     💡 解决方案: ${issue.solution}`))
		})

		// 自动处理关键问题
		const criticalIssues = issues.filter(issue => issue.type === 'critical')
		if (criticalIssues.length > 0) {
			console.log(chalk.blue('\n🔧 正在自动处理关键兼容性问题...'))
			
			try {
				const packageJson = await fs.readJson(this.packageJsonPath)
				const modified = await this.ruleManager.autoFixIssues(packageJson, criticalIssues)

				if (modified) {
					await fs.writeJson(this.packageJsonPath, packageJson, { spaces: 2 })
					console.log(chalk.green('✅ 关键兼容性问题已自动修复'))
				}

			} catch (error) {
				console.error(chalk.red('❌ 自动修复失败:'), error.message)
			}
		}

		return { handled: criticalIssues.length > 0, issues }
	}

	/**
	 * 生成配置文件修改建议
	 */
	generateConfigModificationSuggestions(issues) {
		return this.ruleManager.generateConfigSuggestions(issues)
	}

	/**
	 * 获取需要添加的新依赖
	 */
	getNewDependencies() {
		// 基础必需依赖
		const baseDependencies = {
			'@vue/compiler-sfc': '^3.4.0'
		}

		// 如果不是迁移模式或不保留 Vue 3 依赖，则添加所有默认依赖
		if (!this.isVue3Project || !this.options.preserveVue3Dependencies) {
			return {
				...baseDependencies,
				'element-plus': '^2.9.0',
				'@element-plus/icons-vue': '^2.3.1'
			}
		}

		// Vue 3 项目中只添加基础依赖，其他依赖通过条件规则判断
		return baseDependencies
	}

	/**
	 * 升级 package.json 中的 Vue 相关依赖
	 */
	async upgrade () {
		try {
			console.log(chalk.blue('📦 开始升级 package.json 依赖...'))

			// 加载配置文件
			await this.loadConfig()

			// 检查 package.json 是否存在
			if (!await fs.pathExists(this.packageJsonPath)) {
				throw new Error(`package.json 不存在: ${this.packageJsonPath}`)
			}

			// 首先检查和处理版本兼容性问题
			const compatibilityResult = await this.handleVersionCompatibility()
			
			// 读取 package.json（可能在兼容性处理中被修改）
			const packageJson = await fs.readJson(this.packageJsonPath)

			// 升级依赖
			const result = this.upgradeDependencies(packageJson)

			// 写入更新后的 package.json
			await fs.writeJson(this.packageJsonPath, packageJson, { spaces: 2 })

			console.log(chalk.green('✅ package.json 依赖升级完成!'))
			this.printUpgradeResult(result)

			// 如果有兼容性问题，显示配置修改建议
			if (compatibilityResult.issues.length > 0) {
				this.printConfigModificationSuggestions(compatibilityResult.issues)
				
				// 询问是否自动修复配置文件
				if (this.options.autoFixConfig !== false) {
					console.log(chalk.blue('\n🔧 正在自动修复配置文件...'))
					const fixedFiles = await this.fixConfigFiles(compatibilityResult.issues)
					
					if (fixedFiles.length > 0) {
						console.log(chalk.green(`✅ 已自动修复 ${fixedFiles.length} 个配置文件`))
					} else {
						console.log(chalk.gray('  没有需要修复的配置文件'))
					}
				}
			}

			return {
				...result,
				compatibilityIssues: compatibilityResult.issues
			}
		} catch (error) {
			console.error(chalk.red('❌ package.json 依赖升级失败:'), error.message)
			throw error
		}
	}

	/**
	 * 升级依赖版本
	 */
	upgradeDependencies (packageJson) {
		const result = {
			upgraded: [],
			added: [],
			removed: [],
			unchanged: []
		}

		// 在处理依赖之前，先保存原始依赖信息用于条件判断
		const originalDependencies = {
			...packageJson.dependencies,
			...packageJson.devDependencies
		}

		const dependencyMapping = this.getDependencyMapping()
		const dependenciesToRemove = this.getDependenciesToRemove()
		const newDependencies = this.getNewDependencies()

		// 处理 dependencies
		if (packageJson.dependencies) {
			this.processDependencies(packageJson.dependencies, result, 'dependencies', dependencyMapping, dependenciesToRemove)
		}

		// 处理 devDependencies
		if (packageJson.devDependencies) {
			this.processDependencies(packageJson.devDependencies, result, 'devDependencies', dependencyMapping, dependenciesToRemove)
		}

		// 添加新依赖（使用原始依赖信息进行条件判断）
		this.addNewDependencies(packageJson, result, newDependencies, originalDependencies)

		// 更新脚本
		this.updateScripts(packageJson)

		return result
	}

	/**
	 * 处理依赖对象
	 */
	processDependencies (deps, result, type, dependencyMapping, dependenciesToRemove) {
		Object.keys(deps).forEach(depName => {
			if (dependenciesToRemove.includes(depName)) {
				// 删除依赖
				delete deps[depName]
				result.removed.push({ name: depName, type })
			} else if (dependencyMapping[depName]) {
				// 智能处理依赖升级
				const oldVersion = deps[depName]
				const newVersion = dependencyMapping[depName]
				
				// 如果是 Vue 3 项目且要求保留现有依赖
				if (this.isVue3Project && this.options.preserveVue3Dependencies) {
					// 检查当前版本是否已经是 Vue 3 兼容版本
					if (this.isVue3CompatibleVersion(depName, oldVersion)) {
						console.log(chalk.gray(`  保留 ${depName}@${oldVersion} (已是 Vue 3 兼容版本)`))
						result.unchanged.push({ name: depName, version: oldVersion, type, reason: 'Vue 3 兼容版本已存在' })
						return
					}
				}
				
				// 执行升级
				deps[depName] = newVersion
				result.upgraded.push({
					name: depName,
					oldVersion,
					newVersion,
					type
				})
			} else {
				// 保持不变
				let reason = undefined
				
				// 如果是 Vue 3 项目且在保护列表中，检查是否真的是现代版本
				if (this.isVue3Project && this.options.preserveVue3Dependencies) {
					const protectedPackages = this.config.migrationSettings?.preserveVue3Dependencies?.protectedPackages || []
					if (protectedPackages.includes(depName)) {
						// 检查是否真的是现代版本
						if (this.isVue3CompatibleVersion(depName, deps[depName])) {
							reason = '现代依赖，无需升级'
						} else {
							// 在保护列表中但不是现代版本，可能需要升级
							reason = '在保护列表中，但版本较旧'
						}
					}
				}
				
				result.unchanged.push({ name: depName, version: deps[depName], type, reason })
			}
		})
	}

	/**
	 * 检查版本是否已经是 Vue 3 兼容版本
	 */
	isVue3CompatibleVersion(depName, version) {
		// 从配置文件中获取保护的包列表
		const protectedPackages = this.config.migrationSettings?.preserveVue3Dependencies?.protectedPackages || []
		
		// 如果在保护列表中，使用更智能的版本检查
		if (protectedPackages.includes(depName)) {
			return this.isModernVersion(depName, version)
		}

		// Vue 3 特定版本的判断逻辑
		const vue3CompatibleVersions = {
			'vue': ['3.', '^3.', '~3.'],
			'vue-router': ['4.', '^4.', '~4.'],
			'vuex': ['4.', '^4.', '~4.'],
			'pinia': ['2.', '^2.', '~2.'],
			'element-plus': ['2.', '^2.', '~2.'],
			'@vue/compiler-sfc': ['3.', '^3.', '~3.'],
			'@vue/cli-service': ['5.', '^5.', '~5.'],
			'@vue/test-utils': ['2.', '^2.', '~2.'],
			'eslint-plugin-vue': ['9.', '^9.', '~9.', '10.', '^10.', '~10.'],
			'vite': ['4.', '^4.', '~4.', '5.', '^5.', '~5.'],
			'@vitejs/plugin-vue': ['4.', '^4.', '~4.', '5.', '^5.', '~5.']
		}

		const compatiblePrefixes = vue3CompatibleVersions[depName]
		if (!compatiblePrefixes) {
			// 对于不在列表中的依赖，如果是现代包名或版本，可能兼容
			return this.isLikelyModernDependency(depName, version)
		}

		return compatiblePrefixes.some(prefix => version.startsWith(prefix))
	}

	/**
	 * 检查是否是现代版本（用于保护列表中的包）
	 */
	isModernVersion(depName, version) {
		// 移除版本前缀符号
		const cleanVersion = version.replace(/^[\^~>=<]+/, '')
		const majorVersion = parseInt(cleanVersion.split('.')[0])

		// 对于一些包，现代版本的判断逻辑
		const modernVersionThresholds = {
			'sass': 1, // sass 1.x 都是现代版本，但需要检查小版本
			'sass-loader': 12, // sass-loader 12+ 支持 webpack 5
			'node-sass': 999, // node-sass 已废弃，任何版本都认为需要升级
			'webpack': 5, // webpack 5+
			'typescript': 4, // TypeScript 4+
			'@types/node': 16, // Node types 16+
			'eslint': 8, // ESLint 8+
			'postcss': 8, // PostCSS 8+
			'autoprefixer': 10, // Autoprefixer 10+
		}

		const threshold = modernVersionThresholds[depName]
		if (threshold !== undefined) {
			if (threshold === 999) {
				// 特殊情况：废弃的包
				return false
			}
			
			// 特殊处理 sass：需要检查小版本号
			if (depName === 'sass' && majorVersion === 1) {
				const minorVersion = parseFloat(cleanVersion)
				return minorVersion >= 1.30 // sass 1.30+ 支持更好的 Vue 3 集成
			}
			
			return majorVersion >= threshold
		}

		// 对于其他包，如果版本号较新（主版本号 >= 2），认为可能是现代版本
		return majorVersion >= 2
	}

	/**
	 * 检查是否可能是现代依赖
	 */
	isLikelyModernDependency(depName, version) {
		// 现代包的特征
		const modernPackagePatterns = [
			/^@vue\//,           // Vue 官方包
			/^@vueuse\//,        // VueUse 工具库
			/^@vitejs\//,        // Vite 相关
			/^@types\//,         // TypeScript 类型定义
			/^vue3-/,            // Vue 3 专用包
			/^pinia/,            // Pinia 状态管理
		]

		// 检查包名是否符合现代包模式
		const isModernPackageName = modernPackagePatterns.some(pattern => pattern.test(depName))
		
		if (isModernPackageName) {
			return true
		}

		// 检查版本号是否较新
		const cleanVersion = version.replace(/^[\^~>=<]+/, '')
		const majorVersion = parseInt(cleanVersion.split('.')[0])
		
		// 如果主版本号较大，可能是现代版本
		return majorVersion >= 3
	}

	/**
	 * 添加新依赖
	 */
	addNewDependencies (packageJson, result, newDependencies, originalDependencies = null) {
		if (!packageJson.dependencies) {
			packageJson.dependencies = {}
		}

		// 使用原始依赖信息进行条件判断，如果没有提供则使用当前的
		const depsForConditionCheck = originalDependencies || {
			...packageJson.dependencies,
			...packageJson.devDependencies
		}

		// 获取完整的依赖列表，包括条件依赖
		const allPotentialDependencies = this.getAllPotentialDependencies(depsForConditionCheck, newDependencies)

		Object.entries(allPotentialDependencies).forEach(([depName, version]) => {
			// 检查依赖是否已存在
			const existsInDeps = packageJson.dependencies[depName]
			const existsInDevDeps = packageJson.devDependencies?.[depName]
			
			if (!existsInDeps && !existsInDevDeps) {
				// 检查是否应该添加这个依赖（使用原始依赖信息）
				if (this.shouldAddDependency(depName, { dependencies: depsForConditionCheck, devDependencies: {} })) {
					packageJson.dependencies[depName] = version
					result.added.push({ name: depName, version, type: 'dependencies' })
				} else {
					console.log(chalk.gray(`  跳过添加 ${depName} (不满足添加条件)`))
				}
			}
		})
	}

	/**
	 * 获取所有可能的依赖（包括条件依赖）
	 */
	getAllPotentialDependencies(dependenciesForCheck, baseDependencies) {
		const allDeps = { ...baseDependencies }
		
		// 添加条件依赖
		const conditionalDeps = {
			'element-plus': '^2.9.0',
			'@element-plus/icons-vue': '^2.3.1'
		}

		// 合并所有依赖
		return { ...allDeps, ...conditionalDeps }
	}

	/**
	 * 判断是否应该添加某个依赖
	 */
	shouldAddDependency(depName, packageJson) {
		// 对于基础依赖，总是添加
		const baseDependencies = ['@vue/compiler-sfc']
		if (baseDependencies.includes(depName)) {
			return true
		}

		// 如果是 Vue 3 项目且启用了依赖保护
		if (this.isVue3Project && this.options.preserveVue3Dependencies) {
			return this.isEssentialDependency(depName, packageJson)
		}

		// 其他情况下，检查是否是必要依赖
		return this.isEssentialDependency(depName, packageJson)
	}

	/**
	 * 检查依赖是否是必要的
	 */
	isEssentialDependency(depName, packageJson) {
		const allDeps = {
			...packageJson.dependencies,
			...packageJson.devDependencies
		}

		// 使用配置文件中的条件规则
		const conditionalRules = this.config.migrationSettings?.conditionalDependencies?.rules || {}
		
		if (conditionalRules[depName]) {
			const rule = conditionalRules[depName]
			return this.evaluateCondition(rule.condition, allDeps)
		}

		// 回退到默认逻辑
		// 如果项目使用了 element-ui，则 element-plus 是必要的
		if (depName === 'element-plus') {
			return allDeps['element-ui'] !== undefined
		}

		// 如果项目使用了 element-plus，则图标库是必要的
		if (depName === '@element-plus/icons-vue') {
			return allDeps['element-plus'] !== undefined || allDeps['element-ui'] !== undefined
		}

		// @vue/compiler-sfc 通常是必要的
		if (depName === '@vue/compiler-sfc') {
			return true
		}

		return false
	}

	/**
	 * 评估条件表达式
	 */
	evaluateCondition(condition, allDeps) {
		// 简单的条件评估器
		const hasElementUI = allDeps['element-ui'] !== undefined
		const hasElementPlus = allDeps['element-plus'] !== undefined
		
		// 替换条件中的变量
		const evaluatedCondition = condition
			.replace(/hasElementUI/g, hasElementUI)
			.replace(/hasElementPlus/g, hasElementPlus)
		
		try {
			// 安全地评估简单的布尔表达式
			return Function('"use strict"; return (' + evaluatedCondition + ')')()
		} catch (error) {
			console.warn(chalk.yellow(`⚠️  无法评估条件: ${condition}`))
			return false
		}
	}

	/**
	 * 更新脚本命令
	 */
	updateScripts (packageJson) {
		if (!packageJson.scripts) return

		// 更新构建脚本以使用 Vite（如果需要）
		if (packageJson.scripts.dev && packageJson.scripts.dev.includes('vue-cli-service')) {
			// 保持 vue-cli-service，但可以在后续步骤中提示用户考虑迁移到 Vite
		}
	}

	/**
	 * 打印升级结果
	 */
	printUpgradeResult (result) {
		console.log('\n' + chalk.bold('📊 升级结果统计:'))

		if (result.upgraded.length > 0) {
			console.log(chalk.green(`\n✅ 已升级 (${result.upgraded.length}个):`))
			result.upgraded.forEach(dep => {
				console.log(`  ${dep.name}: ${dep.oldVersion} → ${dep.newVersion}`)
			})
		}

		if (result.added.length > 0) {
			console.log(chalk.blue(`\n➕ 已添加 (${result.added.length}个):`))
			result.added.forEach(dep => {
				console.log(`  ${dep.name}: ${dep.version}`)
			})
		}

		if (result.removed.length > 0) {
			console.log(chalk.red(`\n🗑️  已删除 (${result.removed.length}个):`))
			result.removed.forEach(dep => {
				console.log(`  ${dep.name}`)
			})
		}

		if (result.unchanged.length > 0) {
			console.log(chalk.gray(`\n⏸️  保持不变 (${result.unchanged.length}个):`))
			if (this.options.verbose || result.unchanged.some(dep => dep.reason)) {
				result.unchanged.forEach(dep => {
					if (dep.reason) {
						console.log(chalk.gray(`  ${dep.name}@${dep.version} - ${dep.reason}`))
					} else {
						console.log(chalk.gray(`  ${dep.name}@${dep.version}`))
					}
				})
			}
		}
	}

	/**
	 * 打印配置修改建议
	 */
	printConfigModificationSuggestions(issues) {
		console.log('\n' + chalk.bold('📋 配置修改建议:'))
		const suggestions = this.generateConfigModificationSuggestions(issues)
		
		suggestions.forEach((suggestion, index) => {
			console.log(`${index + 1}. ${suggestion.file}:`)
			console.log(chalk.blue(`  💡 建议: ${suggestion.description}`))
			console.log(chalk.gray(`  原因: ${suggestion.solution}`))
			
			// 显示手动修复步骤
			if (suggestion.manualSteps) {
				console.log(chalk.yellow('  手动修复步骤:'))
				suggestion.manualSteps.forEach(step => {
					console.log(chalk.gray(`    ${step}`))
				})
			}
		})
	}

	/**
	 * 自动修复配置文件
	 */
	async fixConfigFiles(issues) {
		const suggestions = this.generateConfigModificationSuggestions(issues)
		return await this.configFixer.fixConfigFiles(suggestions)
	}
}

module.exports = PackageUpgrader
