const fs = require('fs-extra');
const path = require('path');
const chalk = require('chalk');
const { AIService } = require('./ai-service');

/**
 * BuildFixAgent - AI驱动的构建错误修复代理
 * 
 * 专门负责：
 * - AI调用和错误分析
 * - 工具执行（文件读写、目录列表）
 * - 错误分类和修复策略
 * - 与BuildFixer协作完成修复任务
 */
class BuildFixAgent extends AIService {
  constructor(projectPath, options = {}) {
    const aiOptions = {
      ...options,
      logDir: options.logDir || path.join(projectPath || process.cwd(), 'ai-logs')
    };
    super(aiOptions);

    this.projectPath = projectPath;
    this.options = {
      maxAttempts: 6,
      dryRun: false,
      verbose: false,
      ...options
    };

    // 工具定义 - 类似 Augment 的工具系统
    this.tools = [
      {
        name: 'read_file',
        description: '读取项目中的文件内容',
        parameters: {
          type: 'object',
          properties: {
            file_path: {
              type: 'string',
              description: '相对于项目根目录的文件路径'
            }
          },
          required: ['file_path']
        }
      },
      {
        name: 'write_file',
        description: '写入或修改项目中的文件',
        parameters: {
          type: 'object',
          properties: {
            file_path: {
              type: 'string',
              description: '相对于项目根目录的文件路径'
            },
            content: {
              type: 'string',
              description: '要写入的文件内容'
            }
          },
          required: ['file_path', 'content']
        }
      },
      {
        name: 'list_files',
        description: '列出项目目录中的文件',
        parameters: {
          type: 'object',
          properties: {
            directory: {
              type: 'string',
              description: '要列出的目录路径，相对于项目根目录'
            },
            pattern: {
              type: 'string',
              description: '文件匹配模式，如 \'*.vue\' 或 \'*.js\''
            }
          },
          required: ['directory']
        }
      }
    ];

    // 重复检测
    this.attemptHistory = {
      filesToFix: [], // 记录每次尝试修复的文件列表
      errorHashes: [], // 记录每次的错误哈希
      lastErrorOutput: null // 记录上次的错误输出
    };

    // 修复统计
    this.fixStats = {
      filesAnalyzed: 0,
      filesModified: 0,
      errorsFixed: 0,
      attempts: 0
    };
  }

  /**
   * 分析构建错误并选择需要修复的文件
   */
  async analyzeBuildErrors(buildOutput, attemptNumber = 1) {
    if (!this.isEnabled()) {
      throw new Error('AI 服务不可用，无法进行错误分析');
    }

    try {
      if (this.options.verbose) {
        console.log(chalk.gray(`🔍 构建错误输出长度: ${buildOutput.length} 字符`));
      }

      const prompt = this.generateAnalysisPrompt(buildOutput);
      
      // 添加轮次信息到日志上下文
      const response = await this.callAI(prompt, {
        context: {
          taskType: 'error-analysis',
          attemptNumber: attemptNumber,
          phase: 'analysis',
          buildOutputLength: buildOutput.length
        }
      });

      if (this.options.verbose) {
        console.log(chalk.gray(`🤖 AI 响应长度: ${response.length} 字符`));
      }

      // 解析 AI 响应，提取要修复的文件列表
      const filesToFix = this.parseAnalysisResponse(response);

      if (filesToFix.length === 0) {
        throw new Error('AI 未能识别需要修复的文件');
      }

      console.log(chalk.gray(`✅ AI 识别出 ${filesToFix.length} 个需要修复的文件`));
      filesToFix.forEach(file => {
        console.log(chalk.gray(`  - ${file}`));
      });

      return {
        success: true,
        filesToFix
      };
    } catch (error) {
      console.log(chalk.red(`❌ AI 分析异常: ${error.message}`));
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 修复指定的文件列表
   */
  async fixFiles(filesToFix, buildOutput, attemptNumber = 1) {
    let filesModified = 0;
    const errors = [];

    // 获取之前的尝试记录
    const previousAttempts = this.getPreviousAttempts(filesToFix, attemptNumber);

    for (let fileIndex = 0; fileIndex < filesToFix.length; fileIndex++) {
      const filePath = filesToFix[fileIndex];
      try {
        console.log(chalk.gray(`🔧 修复文件: ${filePath}`));

        // 读取文件内容
        const fileContent = await this.executeToolCall('read_file', { file_path: filePath });

        if (!fileContent.success) {
          console.log(chalk.yellow(`  ⚠️  无法读取文件: ${fileContent.error}`));
          errors.push(`无法读取文件 ${filePath}: ${fileContent.error}`);
          continue;
        }

        // 获取该文件的之前尝试记录
        const filePreviousAttempts = previousAttempts.filter(attempt => attempt.filePath === filePath);

        // 让 AI 修复文件，添加详细的上下文信息
        const fixResult = await this.fixSingleFile(
          filePath,
          fileContent.content,
          buildOutput,
          attemptNumber,
          fileIndex + 1,
          filesToFix.length,
          filePreviousAttempts
        );

        if (fixResult.success) {
          // 写入修复后的文件
          const writeResult = await this.executeToolCall('write_file', {
            file_path: filePath,
            content: fixResult.fixedContent
          });

          if (writeResult.success) {
            console.log(chalk.green('  ✅ 文件修复成功'));
            filesModified++;
            this.fixStats.filesModified++;
          } else {
            console.log(chalk.yellow(`  ⚠️  无法写入文件: ${writeResult.error}`));
            errors.push(`无法写入文件 ${filePath}: ${writeResult.error}`);
          }
        } else {
          console.log(chalk.yellow(`  ⚠️  AI 修复失败: ${fixResult.error}`));

          // 显示 AI 响应的部分内容用于调试
          if (fixResult.aiResponse) {
            console.log(chalk.gray(`     AI 响应预览: ${fixResult.aiResponse}`));
          }

          errors.push(`AI 修复失败 ${filePath}: ${fixResult.error}`);

          // 记录失败的尝试
          this.recordFailedAttempt(filePath, attemptNumber, fixResult.error);
        }
      } catch (error) {
        console.log(chalk.red(`  ❌ 修复异常: ${error.message}`));
        errors.push(`修复异常 ${filePath}: ${error.message}`);

        // 记录异常的尝试
        this.recordFailedAttempt(filePath, attemptNumber, error.message);
      }
    }

    this.fixStats.filesAnalyzed += filesToFix.length;
    this.fixStats.attempts++;

    return {
      filesModified,
      errors,
      totalFiles: filesToFix.length
    };
  }

  /**
   * 执行工具调用
   */
  async executeToolCall(toolName, parameters) {
    try {
      switch (toolName) {
        case 'read_file':
          return await this.readFile(parameters.file_path);
        case 'write_file':
          return await this.writeFile(parameters.file_path, parameters.content);
        case 'list_files':
          return await this.listFiles(parameters.directory, parameters.pattern);
        default:
          return {
            success: false,
            error: `未知的工具: ${toolName}`
          };
      }
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 读取文件工具
   */
  async readFile(filePath) {
    try {
      const fullPath = path.join(this.projectPath, filePath);

      if (!await fs.pathExists(fullPath)) {
        return {
          success: false,
          error: '文件不存在'
        };
      }

      const content = await fs.readFile(fullPath, 'utf8');
      return {
        success: true,
        content
      };
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 写入文件工具
   */
  async writeFile(filePath, content) {
    try {
      if (this.options.dryRun) {
        console.log(chalk.gray(`  [预览模式] 将写入文件: ${filePath}`));
        return {
          success: true,
          message: '预览模式，未实际写入'
        };
      }

      const fullPath = path.join(this.projectPath, filePath);

      // 确保目录存在
      await fs.ensureDir(path.dirname(fullPath));

      // 备份原文件
      if (await fs.pathExists(fullPath)) {
        const backupPath = `${fullPath}.backup.${Date.now()}`;
        await fs.copy(fullPath, backupPath);
      }

      await fs.writeFile(fullPath, content, 'utf8');
      return {
        success: true,
        message: '文件写入成功'
      };
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 列出文件工具
   */
  async listFiles(directory, pattern) {
    try {
      const fullPath = path.join(this.projectPath, directory);

      if (!await fs.pathExists(fullPath)) {
        return {
          success: false,
          error: '目录不存在'
        };
      }

      const files = await fs.readdir(fullPath);
      let filteredFiles = files;

      if (pattern) {
        const glob = require('glob');
        filteredFiles = files.filter(file => glob.minimatch(file, pattern));
      }

      return {
        success: true,
        files: filteredFiles
      };
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 检查是否重复尝试相同的修复
   */
  isRepeatingAttempt(buildOutput) {
    // 生成当前错误的哈希
    const currentErrorHash = this.generateErrorHash(buildOutput);

    // 检查是否已经尝试过相同的错误
    if (this.attemptHistory.errorHashes.includes(currentErrorHash)) {
      return true;
    }

    // 检查错误输出是否与上次完全相同
    if (this.attemptHistory.lastErrorOutput === buildOutput) {
      return true;
    }

    return false;
  }

  /**
   * 生成错误哈希
   */
  generateErrorHash(buildOutput) {
    // 提取关键错误信息
    const errorLines = buildOutput.split('\n').filter(line => {
      const lowerLine = line.toLowerCase();
      return lowerLine.includes('error') ||
             lowerLine.includes('failed') ||
             lowerLine.includes('validationerror');
    });

    // 生成简单哈希
    return errorLines.join('|').replace(/\s+/g, ' ').trim();
  }

  /**
   * 记录本次尝试
   */
  recordAttempt(buildOutput, filesToFix) {
    const errorHash = this.generateErrorHash(buildOutput);

    this.attemptHistory.errorHashes.push(errorHash);
    this.attemptHistory.filesToFix.push([...filesToFix]);
    this.attemptHistory.lastErrorOutput = buildOutput;

    // 只保留最近3次尝试的记录
    if (this.attemptHistory.errorHashes.length > 3) {
      this.attemptHistory.errorHashes.shift();
      this.attemptHistory.filesToFix.shift();
    }
  }

  /**
   * 修复单个文件 - 两轮AI调用模式
   * 第一轮：根据错误生成工具调用，决定需要读取哪些文件
   * 第二轮：基于读取的文件内容和错误信息，生成具体的修复代码
   */
  async fixSingleFile(filePath, fileContent, buildOutput, attemptNumber = 1, fileIndex = 1, totalFiles = 1, previousAttempts = []) {
    try {
      console.log(chalk.gray(`  🔄 开始两轮AI修复流程...`));

      // 第一轮AI调用：分析错误并生成工具调用
      const toolCallsResult = await this.generateToolCalls(filePath, buildOutput, attemptNumber, previousAttempts);

      if (!toolCallsResult.success) {
        return {
          success: false,
          error: `第一轮AI调用失败: ${toolCallsResult.error}`
        };
      }

      // 执行工具调用，收集相关文件内容
      const contextFiles = await this.executeToolCalls(toolCallsResult.toolCalls);

      // 第二轮AI调用：基于收集的文件内容进行修复
      const fixResult = await this.generateFileFix(
        filePath,
        fileContent,
        buildOutput,
        contextFiles,
        attemptNumber,
        fileIndex,
        totalFiles,
        previousAttempts
      );

      return fixResult;

    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 第一轮AI调用：根据构建错误生成工具调用
   */
  async generateToolCalls(filePath, buildOutput, attemptNumber = 1, previousAttempts = []) {
    try {
      console.log(chalk.gray(`    🎯 第一轮AI调用：分析错误并生成工具调用...`));

      const prompt = this.generateToolCallPrompt(filePath, buildOutput, attemptNumber, previousAttempts);

      const response = await this.callAI(prompt, {
        context: {
          taskType: 'tool-generation',
          attemptNumber: attemptNumber,
          phase: 'tool-calls',
          fileName: path.basename(filePath),
          fileType: path.extname(filePath),
          buildOutputLength: buildOutput.length,
          previousAttemptsCount: previousAttempts.length
        }
      });

      // 解析AI响应中的工具调用
      const toolCalls = this.parseToolCallsResponse(response);

      if (toolCalls.length === 0) {
        return {
          success: false,
          error: 'AI未能生成有效的工具调用'
        };
      }

      console.log(chalk.gray(`    ✅ 生成了 ${toolCalls.length} 个工具调用`));
      toolCalls.forEach((call, index) => {
        console.log(chalk.gray(`      ${index + 1}. ${call.name}(${JSON.stringify(call.parameters)})`));
      });

      return {
        success: true,
        toolCalls
      };

    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 执行工具调用，收集相关文件内容
   */
  async executeToolCalls(toolCalls) {
    const contextFiles = new Map();

    console.log(chalk.gray(`    🔧 执行工具调用，收集文件内容...`));

    for (const toolCall of toolCalls) {
      try {
        const result = await this.executeToolCall(toolCall.name, toolCall.parameters);

        if (result.success) {
          if (toolCall.name === 'read_file') {
            contextFiles.set(toolCall.parameters.file_path, {
              content: result.content,
              path: toolCall.parameters.file_path
            });
            console.log(chalk.gray(`      ✅ 读取文件: ${toolCall.parameters.file_path}`));
          } else if (toolCall.name === 'list_files') {
            contextFiles.set(`list_${toolCall.parameters.directory}`, {
              files: result.files,
              directory: toolCall.parameters.directory
            });
            console.log(chalk.gray(`      ✅ 列出目录: ${toolCall.parameters.directory} (${result.files.length} 个文件)`));
          }
        } else {
          console.log(chalk.yellow(`      ⚠️  工具调用失败: ${toolCall.name} - ${result.error}`));
        }
      } catch (error) {
        console.log(chalk.yellow(`      ⚠️  工具调用异常: ${toolCall.name} - ${error.message}`));
      }
    }

    console.log(chalk.gray(`    📁 收集到 ${contextFiles.size} 个上下文文件/目录`));
    return contextFiles;
  }

  /**
   * 第二轮AI调用：基于收集的文件内容生成修复代码
   */
  async generateFileFix(filePath, fileContent, buildOutput, contextFiles, attemptNumber = 1, fileIndex = 1, totalFiles = 1, previousAttempts = []) {
    try {
      console.log(chalk.gray(`    🛠️  第二轮AI调用：生成修复代码...`));

      const prompt = this.generateFixPromptWithContext(
        filePath,
        fileContent,
        buildOutput,
        contextFiles,
        attemptNumber,
        previousAttempts
      );

      const response = await this.callAI(prompt, {
        context: {
          taskType: 'file-fix',
          attemptNumber: attemptNumber,
          phase: 'fix',
          fileName: path.basename(filePath),
          fileType: path.extname(filePath),
          fileIndex: fileIndex,
          totalFiles: totalFiles,
          buildOutputLength: buildOutput.length,
          previousAttemptsCount: previousAttempts.length,
          contextFilesCount: contextFiles.size
        }
      });

      // 显示 AI 响应的摘要信息
      this.displayAIResponseSummary(response, filePath, attemptNumber);

      // 解析修复后的文件内容
      const fixedContent = this.parseFixResponse(response, fileContent);

      if (!fixedContent) {
        // 添加更详细的调试信息
        if (this.options.verbose) {
          console.log(chalk.gray(`    🔍 AI响应解析失败，详细信息:`));
          console.log(chalk.gray(`       响应长度: ${response.length} 字符`));
          console.log(chalk.gray(`       包含XML标签: ${response.includes('<file_fix>')}`));
          console.log(chalk.gray(`       响应前500字符:`));
          console.log(chalk.gray(`       ${response.substring(0, 500)}`));
        }

        return {
          success: false,
          error: 'AI 未能生成有效的修复内容',
          aiResponse: response.substring(0, 200) + '...' // 返回部分响应用于调试
        };
      }

      console.log(chalk.green(`    ✅ 第二轮AI调用成功，生成修复代码`));

      return {
        success: true,
        fixedContent,
        aiResponse: response.substring(0, 200) + '...' // 返回部分响应用于调试
      };

    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 生成第一轮AI调用的提示词：工具调用生成
   */
  generateToolCallPrompt(filePath, buildOutput, attemptNumber = 1, previousAttempts = []) {
    // 生成重试相关的上下文信息
    const retryContext = this.generateRetryContext(attemptNumber, previousAttempts);

    // 截取构建输出，避免过长
    const maxOutputLength = 4000;
    const truncatedOutput = buildOutput.length > maxOutputLength
      ? buildOutput.substring(0, maxOutputLength) + '\n... (输出已截断)'
      : buildOutput;

    return `你是一个专业的 Vue 2 到 Vue 3 迁移专家和构建错误分析师。

**任务目标**：
专注修复单个文件 "${filePath}"，只读取修复这个文件所必需的最少文件。

**当前要修复的文件**：${filePath}
${retryContext}
**构建错误输出**：
\`\`\`
${truncatedOutput}
\`\`\`

**可用工具**：
${this.tools.map(tool => `- ${tool.name}: ${tool.description}`).join('\n')}

**分析要求**：
1. **专注单一文件**：只关注修复 "${filePath}" 这一个文件
2. **最少依赖原则**：只读取修复此文件绝对必要的文件（通常1-3个）
3. **优先级排序**：
   - 首先读取要修复的文件本身
   - 然后读取直接相关的依赖文件（如导入的模块、样式文件）
   - 最后考虑配置文件（仅在必要时）

**响应格式**：
请使用以下 XML 格式返回工具调用列表（限制在3个以内）：

\`\`\`xml
<tool_calls>
<call>
<name>read_file</name>
<parameters>
<file_path>${filePath}</file_path>
</parameters>
</call>
<call>
<name>read_file</name>
<parameters>
<file_path>src/styles/mixin.scss</file_path>
</parameters>
</call>
</tool_calls>
\`\`\`

**重要约束**：
- **最多生成3个工具调用**
- **第一个调用必须是读取目标文件本身**
- **只读取与当前错误直接相关的文件**
- **不要读取 package.json、vue.config.js 等配置文件，除非错误明确指向它们**
- **专注解决当前文件的具体错误，不要试图一次性解决所有问题**

**错误类型分析**：
- 如果是 "Undefined mixin" 错误 → 读取 mixin 定义文件
- 如果是 "Undefined variable" 错误 → 读取变量定义文件
- 如果是导入错误 → 读取被导入的文件
- 如果是组件错误 → 读取组件定义文件

保持简单、精准、高效！`;
  }

  /**
   * 解析工具调用响应
   */
  parseToolCallsResponse(response) {
    try {
      const toolCalls = [];

      // 尝试解析 XML 格式
      const xmlMatch = response.match(/<tool_calls>([\s\S]*?)<\/tool_calls>/);

      if (xmlMatch) {
        const callsSection = xmlMatch[1];
        const callMatches = callsSection.match(/<call>([\s\S]*?)<\/call>/g);

        if (callMatches) {
          for (const callMatch of callMatches) {
            const nameMatch = callMatch.match(/<name>(.*?)<\/name>/);
            const parametersMatch = callMatch.match(/<parameters>([\s\S]*?)<\/parameters>/);

            if (nameMatch && parametersMatch) {
              const toolName = nameMatch[1].trim();
              const parameters = {};

              // 解析参数
              const paramSection = parametersMatch[1];
              const paramMatches = paramSection.match(/<(\w+)>(.*?)<\/\w+>/g);

              if (paramMatches) {
                for (const paramMatch of paramMatches) {
                  const paramKeyMatch = paramMatch.match(/<(\w+)>(.*?)<\/\w+>/);
                  if (paramKeyMatch) {
                    parameters[paramKeyMatch[1]] = paramKeyMatch[2].trim();
                  }
                }
              }

              toolCalls.push({
                name: toolName,
                parameters
              });
            }
          }
        }
      }

      return toolCalls;
    } catch (error) {
      console.warn(chalk.yellow('⚠️  解析工具调用响应失败'));
      return [];
    }
  }

  /**
   * 生成第二轮AI调用的提示词：基于上下文的文件修复
   */
  generateFixPromptWithContext(filePath, fileContent, buildOutput, contextFiles, attemptNumber = 1, previousAttempts = []) {
    // 生成重试相关的上下文信息
    const retryContext = this.generateRetryContext(attemptNumber, previousAttempts);

    // 截取构建输出，避免过长
    const maxOutputLength = 3000;
    const truncatedBuildOutput = buildOutput.length > maxOutputLength
      ? buildOutput.substring(0, maxOutputLength) + '\n... (输出已截断)'
      : buildOutput;

    // 构建上下文文件信息
    let contextSection = '';
    if (contextFiles.size > 0) {
      contextSection = '\n**相关文件上下文**：\n';

      for (const [key, fileInfo] of contextFiles) {
        if (fileInfo.content) {
          // 文件内容
          const truncatedContent = fileInfo.content.length > 1000
            ? fileInfo.content.substring(0, 1000) + '\n... (内容已截断)'
            : fileInfo.content;

          contextSection += `\n**文件**: ${fileInfo.path}\n\`\`\`\n${truncatedContent}\n\`\`\`\n`;
        } else if (fileInfo.files) {
          // 目录列表
          contextSection += `\n**目录**: ${fileInfo.directory}\n文件列表: ${fileInfo.files.join(', ')}\n`;
        }
      }
    }

    const fileExtension = path.extname(filePath);

    // 分析错误类型，生成针对性的提示词
    const errorAnalysis = this.analyzeErrorType(buildOutput);

    if (errorAnalysis.isWebpackConfigError) {
      return this.generateWebpackConfigFixPromptWithContext(
        filePath,
        fileContent,
        buildOutput,
        contextSection,
        errorAnalysis,
        attemptNumber,
        previousAttempts
      );
    }

    return `你是一个专业的 Vue 2 到 Vue 3 迁移专家。基于提供的上下文信息，修复以下文件中的构建错误。

**任务目标**：修复 Vue 2 到 Vue 3 迁移过程中的构建错误，确保项目能够成功构建。

**文件信息**：
- 文件路径：${filePath}
- 文件类型：${fileExtension}
${retryContext}
**构建错误输出**：
\`\`\`
${truncatedBuildOutput}
\`\`\`
${contextSection}

**当前文件内容**：
\`\`\`${fileExtension.slice(1) || 'text'}
${fileContent}
\`\`\`

**修复要求**：
1. 基于提供的上下文信息进行精准修复
2. 保持原有功能不变
3. 使用 Vue 3 兼容的语法
4. 更新导入语句和组件使用方式
5. 修复 TypeScript 类型错误
6. 确保代码风格一致

**响应格式**：
请使用以下 XML 格式返回修复后的完整文件内容：

\`\`\`xml
<file_fix>
<path>${filePath}</path>
<content>
修复后的完整文件内容
</content>
</file_fix>
\`\`\`

**常见修复模式**：
- Vue 2 → Vue 3: new Vue() → createApp()
- Element UI → Element Plus: 更新导入路径和组件名称
- Vue Router: 更新路由配置语法
- Vuex: 更新状态管理语法
- 组合式 API: 使用 ref, reactive, computed 等
- 模块导入: 添加缺失的 require/import 语句

请仔细分析错误信息和上下文，提供准确的修复方案。`;
  }

  /**
   * 生成错误分析提示词
   */
  generateAnalysisPrompt(buildOutput) {
    // 截取构建输出，避免过长
    const maxOutputLength = 5000;
    const truncatedOutput = buildOutput.length > maxOutputLength
      ? buildOutput.substring(0, maxOutputLength) + '\n... (输出已截断)'
      : buildOutput;

    return `你是一个专业的 Vue 2 到 Vue 3 迁移专家和构建错误分析师。请分析以下构建错误输出，并确定需要修复的文件。

**任务目标**：
1. 分析构建错误输出
2. 识别导致错误的具体文件
3. 返回需要修复的文件路径列表

**构建错误输出**：
\`\`\`
${truncatedOutput}
\`\`\`

**工具可用**：
你可以使用以下工具来帮助分析：
${this.tools.map(tool => `- ${tool.name}: ${tool.description}`).join('\n')}

**响应格式**：
请使用以下 XML 格式返回分析结果：

\`\`\`xml
<analysis>
<files_to_fix>
<file>src/components/Example.vue</file>
<file>src/utils/helper.js</file>
</files_to_fix>
<reasoning>
简要说明为什么选择这些文件进行修复
</reasoning>
</analysis>
\`\`\`

请仔细分析错误信息，重点关注：
- 文件路径和行号信息
- 模块导入错误
- Vue 2/3 兼容性问题
- TypeScript 类型错误
- 依赖包问题
- Webpack 配置错误

**重要约束**：
- 只返回项目源代码文件，不要包含 node_modules 中的文件
- 对于 Webpack 配置错误，应该检查 vue.config.js、webpack.config.js 等配置文件
- 对于插件错误，重点关注项目配置而非第三方库内部文件
- 文件路径应该相对于项目根目录

只返回确实需要修改代码的文件，不要包含 node_modules 或系统文件。`;
  }

  /**
   * 解析 AI 分析响应
   */
  parseAnalysisResponse(response) {
    try {
      // 尝试解析 XML 格式
      const xmlMatch = response.match(/<analysis>[\s\S]*?<files_to_fix>([\s\S]*?)<\/files_to_fix>[\s\S]*?<\/analysis>/);

      if (xmlMatch) {
        const filesSection = xmlMatch[1];
        const fileMatches = filesSection.match(/<file>(.*?)<\/file>/g);

        if (fileMatches) {
          return fileMatches.map(match => {
            let file = match.replace(/<\/?file>/g, '').trim();
            // 如果是绝对路径，转换为相对路径
            if (file.startsWith(this.projectPath)) {
              file = path.relative(this.projectPath, file);
            }
            return file;
          }).filter(file => this.isValidProjectFile(file));
        }
      }

      // 回退：尝试从响应中提取文件路径
      const lines = response.split('\n');
      const files = [];

      for (const line of lines) {
        // 查找看起来像文件路径的行
        const fileMatch = line.match(/(?:src\/|\.\/)?[\w/\-.]+\.(vue|js|ts|jsx|tsx)$/);
        if (fileMatch) {
          files.push(fileMatch[0]);
        }
      }

      return [...new Set(files)].filter(file => this.isValidProjectFile(file)); // 去重并过滤
    } catch (error) {
      console.warn(chalk.yellow('⚠️  解析 AI 分析响应失败，使用空列表'));
      return [];
    }
  }

  /**
   * 验证文件是否为有效的项目文件
   */
  isValidProjectFile(filePath) {
    if (!filePath || typeof filePath !== 'string') {
      return false;
    }

    // 过滤掉 node_modules 中的文件
    if (filePath.includes('node_modules')) {
      console.log(chalk.yellow(`  ⚠️  跳过 node_modules 文件: ${filePath}`));
      return false;
    }

    // 过滤掉系统路径
    if (path.isAbsolute(filePath) && !filePath.startsWith(this.projectPath)) {
      console.log(chalk.yellow(`  ⚠️  跳过系统文件: ${filePath}`));
      return false;
    }

    // 只允许特定类型的文件
    const allowedExtensions = ['.vue', '.js', '.ts', '.jsx', '.tsx', '.json', '.scss'];
    const allowedConfigFiles = ['vue.config.js', 'webpack.config.js', 'vite.config.js', 'vite.config.ts'];

    const ext = path.extname(filePath);
    const fileName = path.basename(filePath);

    if (allowedExtensions.includes(ext) || allowedConfigFiles.includes(fileName)) {
      return true;
    }

    console.log(chalk.yellow(`  ⚠️  跳过不支持的文件类型: ${filePath}`));
    return false;
  }

  /**
   * 生成文件修复提示词
   */
  generateFixPrompt(filePath, fileContent, buildOutput, attemptNumber = 1, previousAttempts = []) {
    const fileExtension = path.extname(filePath);

    // 分析错误类型，生成针对性的提示词
    const errorAnalysis = this.analyzeErrorType(buildOutput);

    if (errorAnalysis.isWebpackConfigError) {
      return this.generateWebpackConfigFixPrompt(filePath, fileContent, buildOutput, errorAnalysis, attemptNumber, previousAttempts);
    }

    return this.generateGeneralFixPrompt(filePath, fileContent, buildOutput, fileExtension, attemptNumber, previousAttempts);
  }

  /**
   * 分析错误类型
   */
  analyzeErrorType(buildOutput) {
    const analysis = {
      isWebpackConfigError: false,
      isPluginError: false,
      isLoaderError: false,
      isSyntaxError: false,
      isImportError: false,
      specificErrors: []
    };

    const lowerBuildOutput = buildOutput.toLowerCase();

    // 检查是否是 webpack 配置错误
    if (lowerBuildOutput.includes('validationerror: invalid configuration object') ||
        lowerBuildOutput.includes('configuration.plugins') ||
        lowerBuildOutput.includes('misses the property \'apply\'') ||
        lowerBuildOutput.includes('configuration.plugins\\[(\\d+)\\] misses the property \'apply\'') ||
        lowerBuildOutput.includes('cannot call .tap() on a plugin that has not yet been defined')) {
      analysis.isWebpackConfigError = true;
      analysis.isPluginError = true;
    }

    // 检查 webpack is not defined
    if (lowerBuildOutput.includes('referenceerror: webpack is not defined')) {
      analysis.isWebpackConfigError = true;
      analysis.specificErrors.push({
        type: 'webpack_not_defined',
        message: 'ReferenceError: webpack is not defined'
      });
    }

    // 检查 path is not defined
    if (lowerBuildOutput.includes('referenceerror: path is not defined')) {
      analysis.isWebpackConfigError = true;
      analysis.specificErrors.push({
        type: 'path_not_defined',
        message: 'ReferenceError: path is not defined'
      });
    }

    // 检查 Webpack 5 polyfill 问题
    const polyfillMatches = buildOutput.match(/Module not found: Error: Can't resolve '([^']+)'/g);
    if (polyfillMatches) {
      const missingModules = [];
      polyfillMatches.forEach(match => {
        const moduleMatch = match.match(/Can't resolve '([^']+)'/);
        if (moduleMatch) {
          const moduleName = moduleMatch[1];
          // 检查是否是 Node.js 核心模块
          const nodeModules = ['path', 'stream', 'crypto', 'buffer', 'util', 'fs', 'os', 'http', 'https', 'url', 'querystring'];
          if (nodeModules.includes(moduleName)) {
            missingModules.push(moduleName);
          }
        }
      });
      
      if (missingModules.length > 0) {
        analysis.isWebpackConfigError = true;
        analysis.specificErrors.push({
          type: 'webpack5_polyfill_missing',
          modules: [...new Set(missingModules)], // 去重
          message: `Webpack 5 polyfill missing for: ${missingModules.join(', ')}`
        });
      }
    }

    // 检查无效选项
    const invalidOptionMatch = buildOutput.match(/Invalid options in .*? "(.+?)" is not allowed/);
    if (invalidOptionMatch) {
      analysis.isWebpackConfigError = true;
      analysis.specificErrors.push({
        type: 'invalid_option',
        option: invalidOptionMatch[1],
        message: invalidOptionMatch[0]
      });
    }
    
    // 检查 webpack-chain tap 错误
    if (lowerBuildOutput.includes('cannot call .tap() on a plugin that has not yet been defined')) {
      analysis.isWebpackConfigError = true;
      analysis.specificErrors.push({
        type: 'webpack_chain_tap_error',
        message: 'Cannot call .tap() on a plugin that has not yet been defined'
      });
    }

    // 提取具体的插件错误信息
    const pluginErrorMatch = buildOutput.match(/configuration\\.plugins\\[(\\d+)\\] misses the property 'apply'/);
    if (pluginErrorMatch) {
      analysis.specificErrors.push({
        type: 'plugin_missing_apply',
        pluginIndex: parseInt(pluginErrorMatch[1]),
        message: pluginErrorMatch[0]
      });
    }

    // 检查其他错误类型
    if (buildOutput.includes('Module not found') || buildOutput.includes('Cannot resolve module')) {
      analysis.isImportError = true;
    }

    if (buildOutput.includes('SyntaxError') || buildOutput.includes('Unexpected token')) {
      analysis.isSyntaxError = true;
    }

    return analysis;
  }

  /**
   * 生成带上下文的 webpack 配置修复提示词
   */
  generateWebpackConfigFixPromptWithContext(filePath, fileContent, buildOutput, contextSection, errorAnalysis, attemptNumber = 1, previousAttempts = []) {
    // 生成重试相关的上下文信息
    const retryContext = this.generateRetryContext(attemptNumber, previousAttempts);

    // 截断构建输出，避免过长
    const maxOutputLength = 3000; // webpack 配置错误通常比较简洁
    const truncatedBuildOutput = buildOutput.length > maxOutputLength
      ? buildOutput.substring(0, maxOutputLength) + '\n... (输出已截断)'
      : buildOutput;

    let specificGuidance = `
**首要修复任务**：请对整个 'vue.config.js' 文件进行全面审查和修复，使其与 Vue CLI 5 和 Vue 3 完全兼容。不要只修复一个错误，请一次性解决所有潜在问题。`;

    if (errorAnalysis.specificErrors.length > 0) {
      specificGuidance += '\n\n**具体错误分析**：\n';

      for (const error of errorAnalysis.specificErrors) {
        switch (error.type) {
          case 'webpack_not_defined': {
            specificGuidance += `- **错误**: 'webpack is not defined'.\n  - **修复**: 在文件顶部添加 \`const webpack = require('webpack');\`.\n`;
            break;
          }
          case 'path_not_defined': {
            specificGuidance += `- **错误**: 'path is not defined'.\n  - **修复**: 在文件顶部添加 \`const path = require('path');\`.\n`;
            break;
          }
          case 'webpack5_polyfill_missing': {
            const modules = error.modules || [];
            const polyfillPackages = modules.map(mod => `${mod}-browserify`).join(' ');
            specificGuidance += `- **错误**: Webpack 5 缺少 Node.js 核心模块 polyfill: ${modules.join(', ')}.\n`;
            specificGuidance += `  - **修复步骤**:\n`;
            specificGuidance += `    1. 安装 polyfill 包: \`npm install -D ${polyfillPackages}\`\n`;
            specificGuidance += `    2. 在 configureWebpack.resolve.fallback 中配置:\n`;
            modules.forEach(mod => {
              specificGuidance += `       "${mod}": require.resolve("${mod}-browserify"),\n`;
            });
            break;
          }
          case 'invalid_option': {
            specificGuidance += `- **错误**: 无效的配置项 '${error.option}'.\n  - **修复**: 从配置中移除 \`${error.option}\` 属性。\n`;
            break;
          }
          case 'plugin_missing_apply': {
            specificGuidance += `- **错误**: 插件缺少 'apply' 方法 (plugins[${error.pluginIndex}]).\n  - **修复**: 检查该插件的配置，确保传入的是一个有效的插件实例，而不是空对象。\n`;
            break;
          }
          case 'webpack_chain_tap_error': {
            specificGuidance += `- **错误**: 在未定义的插件上调用 .tap()。\n  - **修复**: Vue CLI v5 已内置 preload 插件，通常不再需要手动配置。请移除关于 'preload' 和 'prefetch' 的 chainWebpack 修改。如果确实需要，请确保先使用 \`.use()\` 添加插件。\n`;
            break;
          }
        }
      }
    }

    return `你是一个顶级的 Vue 迁移专家，精通 webpack 和 Vue CLI 配置。基于提供的上下文信息，修复以下 webpack 配置文件中的所有错误。

**任务目标**：全面修复 'vue.config.js'，使其兼容 Vue CLI 5 和 Vue 3，确保项目能成功构建。

**文件路径**：${filePath}
${retryContext}
**构建错误日志**：
\`\`\`
${truncatedBuildOutput}
\`\`\`
${contextSection}
${specificGuidance}

**当前文件内容**：
\`\`\`js
${fileContent}
\`\`\`

**webpack 配置修复清单 (必须全部检查)**：
1.  **导入模块**: 确保文件顶部分别有 \`const webpack = require('webpack');\` 和 \`const path = require('path');\`（如果用到了的话）。
2.  **Webpack 5 Polyfill**: 如果遇到 "Module not found: Error: Can't resolve 'path'" 等错误，需要配置 Node.js 核心模块的 polyfill。
3.  **移除无效选项**: 移除根级别不支持的配置项，例如 \`name\`。Vue CLI 5 的配置项更加严格。
4.  **修复 chainWebpack**:
    - Vue CLI 5 默认处理了 preload/prefetch 插件。请删除 \`config.plugin('preload').tap(...)\` 和 \`config.plugins.delete('prefetch')\` 的相关代码。
    - 检查其他插件的使用，确保语法正确。
5.  **devServer**: 确保 \`devServer.before\` 的用法是正确的。在 webpack-dev-server v4 (Vue CLI 5 使用) 中，它已被替换为 \`devServer.setupMiddlewares\`。请进行相应迁移。
6.  **输出为 Vue 3 格式**: 确保整个文件都是有效的 Vue 3 和 Vue CLI 5 配置。

**Webpack 5 Polyfill 配置示例**：
如果需要 Node.js 核心模块 polyfill，请在 configureWebpack 中添加：
\`\`\`js
configureWebpack: {
  resolve: {
    fallback: {
      "path": require.resolve("path-browserify"),
      "stream": require.resolve("stream-browserify"),
      // 根据需要添加其他模块
    }
  }
}
\`\`\`

**响应格式**：
请在下方 XML 格式的 <content> 标签内，返回修复后的【完整文件内容】。

\`\`\`xml
<file_fix>
<path>${filePath}</path>
<content>
// 在这里放入修复后的完整文件内容
</content>
</file_fix>
\`\`\`

请仔细分析并一次性修复所有问题。`;
  }

  /**
   * 生成 webpack 配置修复提示词
   */
  generateWebpackConfigFixPrompt(filePath, fileContent, buildOutput, errorAnalysis, attemptNumber = 1, previousAttempts = []) {
    // 生成重试相关的上下文信息
    const retryContext = this.generateRetryContext(attemptNumber, previousAttempts);

    // 截断构建输出，避免过长
    const maxOutputLength = 3000; // webpack 配置错误通常比较简洁
    const truncatedBuildOutput = buildOutput.length > maxOutputLength
      ? buildOutput.substring(0, maxOutputLength) + '\n... (输出已截断)'
      : buildOutput;

    let specificGuidance = `
**首要修复任务**：请对整个 'vue.config.js' 文件进行全面审查和修复，使其与 Vue CLI 5 和 Vue 3 完全兼容。不要只修复一个错误，请一次性解决所有潜在问题。`;

    if (errorAnalysis.specificErrors.length > 0) {
      specificGuidance += '\n\n**具体错误分析**：\n';

      for (const error of errorAnalysis.specificErrors) {
        switch (error.type) {
          case 'webpack_not_defined': {
            specificGuidance += `- **错误**: 'webpack is not defined'.\n  - **修复**: 在文件顶部添加 \`const webpack = require('webpack');\`.\n`;
            break;
          }
          case 'path_not_defined': {
            specificGuidance += `- **错误**: 'path is not defined'.\n  - **修复**: 在文件顶部添加 \`const path = require('path');\`.\n`;
            break;
          }
          case 'webpack5_polyfill_missing': {
            const modules = error.modules || [];
            const polyfillPackages = modules.map(mod => `${mod}-browserify`).join(' ');
            specificGuidance += `- **错误**: Webpack 5 缺少 Node.js 核心模块 polyfill: ${modules.join(', ')}.\n`;
            specificGuidance += `  - **修复步骤**:\n`;
            specificGuidance += `    1. 安装 polyfill 包: \`npm install -D ${polyfillPackages}\`\n`;
            specificGuidance += `    2. 在 configureWebpack.resolve.fallback 中配置:\n`;
            modules.forEach(mod => {
              specificGuidance += `       "${mod}": require.resolve("${mod}-browserify"),\n`;
            });
            break;
          }
          case 'invalid_option': {
            specificGuidance += `- **错误**: 无效的配置项 '${error.option}'.\n  - **修复**: 从配置中移除 \`${error.option}\` 属性。\n`;
            break;
          }
          case 'plugin_missing_apply': {
            specificGuidance += `- **错误**: 插件缺少 'apply' 方法 (plugins[${error.pluginIndex}]).\n  - **修复**: 检查该插件的配置，确保传入的是一个有效的插件实例，而不是空对象。\n`;
            break;
          }
          case 'webpack_chain_tap_error': {
            specificGuidance += `- **错误**: 在未定义的插件上调用 .tap()。\n  - **修复**: Vue CLI v5 已内置 preload 插件，通常不再需要手动配置。请移除关于 'preload' 和 'prefetch' 的 chainWebpack 修改。如果确实需要，请确保先使用 \`.use()\` 添加插件。\n`;
            break;
          }
        }
      }
    }

    return `你是一个顶级的 Vue 迁移专家，精通 webpack 和 Vue CLI 配置。请修复以下 webpack 配置文件中的所有错误。

**任务目标**：全面修复 'vue.config.js'，使其兼容 Vue CLI 5 和 Vue 3，确保项目能成功构建。

**文件路径**：${filePath}
${retryContext}
**构建错误日志**：
\`\`\`
${truncatedBuildOutput}
\`\`\`
${specificGuidance}

**当前文件内容**：
\`\`\`js
${fileContent}
\`\`\`

**webpack 配置修复清单 (必须全部检查)**：
1.  **导入模块**: 确保文件顶部分别有 \`const webpack = require('webpack');\` 和 \`const path = require('path');\`（如果用到了的话）。
2.  **Webpack 5 Polyfill**: 如果遇到 "Module not found: Error: Can't resolve 'path'" 等错误，需要配置 Node.js 核心模块的 polyfill。
3.  **移除无效选项**: 移除根级别不支持的配置项，例如 \`name\`。Vue CLI 5 的配置项更加严格。
4.  **修复 chainWebpack**: 
    - Vue CLI 5 默认处理了 preload/prefetch 插件。请删除 \`config.plugin('preload').tap(...)\` 和 \`config.plugins.delete('prefetch')\` 的相关代码。
    - 检查其他插件的使用，确保语法正确。
5.  **devServer**: 确保 \`devServer.before\` 的用法是正确的。在 webpack-dev-server v4 (Vue CLI 5 使用) 中，它已被替换为 \`devServer.setupMiddlewares\`。请进行相应迁移。
6.  **输出为 Vue 3 格式**: 确保整个文件都是有效的 Vue 3 和 Vue CLI 5 配置。

**Webpack 5 Polyfill 配置示例**：
如果需要 Node.js 核心模块 polyfill，请在 configureWebpack 中添加：
\`\`\`js
configureWebpack: {
  resolve: {
    fallback: {
      "path": require.resolve("path-browserify"),
      "stream": require.resolve("stream-browserify"),
      // 根据需要添加其他模块
    }
  }
}
\`\`\`

**响应格式**：
请在下方 XML 格式的 <content> 标签内，返回修复后的【完整文件内容】。

\`\`\`xml
<file_fix>
<path>${filePath}</path>
<content>
// 在这里放入修复后的完整文件内容
</content>
</file_fix>
\`\`\`

请仔细分析并一次性修复所有问题。`;
  }

  /**
   * 生成通用修复提示词
   */
  generateGeneralFixPrompt(filePath, fileContent, buildOutput, fileExtension, attemptNumber = 1, previousAttempts = []) {
    // 生成重试相关的上下文信息
    const retryContext = this.generateRetryContext(attemptNumber, previousAttempts);

    // 截断构建输出，避免过长
    const maxOutputLength = 4000; // 通用修复需要更多上下文
    const truncatedBuildOutput = buildOutput.length > maxOutputLength
      ? buildOutput.substring(0, maxOutputLength) + '\n... (输出已截断)'
      : buildOutput;

    // 检查是否是模块导入相关的错误
    let importGuidance = '';
    if (buildOutput.includes('is not defined') || buildOutput.includes('Cannot access')) {
      importGuidance = `
**模块导入问题检测**：
检测到可能的模块导入错误，请特别注意：
- 检查所有外部模块是否都有正确的导入语句
- 确保 require/import 语句在文件顶部
- 检查模块名称拼写是否正确
- 确保依赖包已正确安装

**常见导入修复**：
- 添加缺失的导入：const webpack = require('webpack')
- 修复 path 导入：const path = require('path')
- 检查所有 require 语句的位置和正确性`;
    }

    return `你是一个专业的 Vue 2 到 Vue 3 迁移专家。请修复以下文件中的构建错误。

**任务目标**：修复 Vue 2 到 Vue 3 迁移过程中的构建错误，确保项目能够成功构建。

**文件信息**：
- 文件路径：${filePath}
- 文件类型：${fileExtension}
${retryContext}
**构建错误输出**：
\`\`\`
${truncatedBuildOutput}
\`\`\`
${importGuidance}

**当前文件内容**：
\`\`\`${fileExtension.slice(1) || 'text'}
${fileContent}
\`\`\`

**修复要求**：
1. 保持原有功能不变
2. 使用 Vue 3 兼容的语法
3. 更新导入语句和组件使用方式
4. 修复 TypeScript 类型错误
5. 确保代码风格一致
6. **重要**：检查并修复所有模块导入问题

**响应格式**：
请使用以下 XML 格式返回修复后的完整文件内容：

\`\`\`xml
<file_fix>
<path>${filePath}</path>
<content>
修复后的完整文件内容
</content>
</file_fix>
\`\`\`

**常见修复模式**：
- Vue 2 → Vue 3: new Vue() → createApp()
- Element UI → Element Plus: 更新导入路径和组件名称
- Vue Router: 更新路由配置语法
- Vuex: 更新状态管理语法
- 组合式 API: 使用 ref, reactive, computed 等
- **模块导入**: 添加缺失的 require/import 语句

**特别注意**：
- 如果错误包含 "is not defined"，检查是否缺少相应的导入语句
- 确保所有外部依赖都有正确的导入
- 检查导入语句的位置和顺序

请仔细分析错误信息，提供准确的修复方案。`;
  }

  /**
   * 解析修复响应
   */
  parseFixResponse(response, originalContent) {
    try {
      // 尝试解析 XML 格式
      const xmlMatch = response.match(/<file_fix>[\s\S]*?<content>([\s\S]*?)<\/content>[\s\S]*?<\/file_fix>/);

      if (xmlMatch) {
        const content = xmlMatch[1].trim();
        if (content) {
          // 改进的内容验证逻辑
          return this.validateFixedContent(content, originalContent);
        }
      }

      // 回退：尝试解析代码块格式
      const codeBlockMatch = response.match(/```(?:vue|js|ts|javascript|typescript)?\s*([\s\S]*?)\s*```/);

      if (codeBlockMatch) {
        const content = codeBlockMatch[1].trim();
        if (content) {
          return this.validateFixedContent(content, originalContent);
        }
      }

      return null;
    } catch (error) {
      console.warn(chalk.yellow('⚠️  解析 AI 修复响应失败'));
      return null;
    }
  }

  /**
   * 验证修复后的内容
   */
  validateFixedContent(fixedContent, originalContent) {
    if (!fixedContent) {
      return null;
    }

    // 标准化内容进行比较（移除多余空格和换行）
    const normalizeContent = (content) => {
      return content
        .replace(/\r\n/g, '\n') // 统一换行符
        .replace(/\s+$/gm, '') // 移除行尾空格
        .trim(); // 移除首尾空格
    };

    const normalizedFixed = normalizeContent(fixedContent);
    const normalizedOriginal = normalizeContent(originalContent);

    // 如果内容完全相同，检查是否是有意的"无需修复"响应
    if (normalizedFixed === normalizedOriginal) {
      // 检查响应中是否包含"无需修复"的指示
      const noFixIndicators = [
        '无需修复',
        'no fix needed',
        'already correct',
        '已经正确',
        'file is correct'
      ];

      // 如果 AI 明确表示无需修复，则返回原内容（表示处理成功）
      // 否则返回 null（表示修复失败）
      const hasNoFixIndicator = noFixIndicators.some(indicator =>
        fixedContent.toLowerCase().includes(indicator.toLowerCase())
      );

      if (hasNoFixIndicator) {
        console.log(chalk.gray('  ℹ️  AI 判断文件无需修复'));
        return originalContent;
      } else {
        console.log(chalk.yellow('  ⚠️  AI 返回的内容与原文件相同，可能修复失败'));
        return null;
      }
    }

    // 基本的内容有效性检查
    if (normalizedFixed.length < 10) {
      console.log(chalk.yellow('  ⚠️  修复后的内容过短，可能无效'));
      return null;
    }

    // 检查是否包含基本的文件结构（针对不同文件类型）
    const hasValidStructure = this.validateFileStructure(fixedContent, originalContent);
    if (!hasValidStructure) {
      console.log(chalk.yellow('  ⚠️  修复后的内容缺少必要的文件结构'));
      return null;
    }

    return fixedContent;
  }

  /**
   * 验证文件结构的有效性
   */
  validateFileStructure(fixedContent, originalContent) {
    if (this.options.verbose) {
      console.log(chalk.gray(`    🔍 验证文件结构...`));
      console.log(chalk.gray(`       原文件长度: ${originalContent.length} 字符`));
      console.log(chalk.gray(`       修复后长度: ${fixedContent.length} 字符`));
    }

    // 对于 Vue 文件，检查基本结构
    if (originalContent.includes('<template>') || originalContent.includes('<script>')) {
      const hasTemplate = fixedContent.includes('<template>') || !originalContent.includes('<template>');
      const hasScript = fixedContent.includes('<script>') || !originalContent.includes('<script>');

      if (this.options.verbose) {
        console.log(chalk.gray(`       Vue文件检查: template=${hasTemplate}, script=${hasScript}`));
        console.log(chalk.gray(`       原文件包含template: ${originalContent.includes('<template>')}`));
        console.log(chalk.gray(`       原文件包含script: ${originalContent.includes('<script>')}`));
        console.log(chalk.gray(`       修复后包含template: ${fixedContent.includes('<template>')}`));
        console.log(chalk.gray(`       修复后包含script: ${fixedContent.includes('<script>')}`));
      }

      return hasTemplate && hasScript;
    }

    // 对于 JS/TS 文件，检查基本的导入导出结构
    if (originalContent.includes('export') || originalContent.includes('module.exports')) {
      const hasExport = fixedContent.includes('export') || fixedContent.includes('module.exports');

      if (this.options.verbose) {
        console.log(chalk.gray(`       JS/TS文件检查: export=${hasExport}`));
      }

      return hasExport;
    }

    // 对于 SCSS/CSS 文件，检查基本的样式结构
    if (originalContent.includes('{') && originalContent.includes('}')) {
      const openBraces = (fixedContent.match(/\{/g) || []).length;
      const closeBraces = (fixedContent.match(/\}/g) || []).length;
      const isValid = openBraces === closeBraces && openBraces > 0;

      if (this.options.verbose) {
        console.log(chalk.gray(`       CSS/SCSS文件检查: 开括号=${openBraces}, 闭括号=${closeBraces}, 有效=${isValid}`));
      }

      return isValid;
    }

    // 对于其他文件类型，默认认为有效
    if (this.options.verbose) {
      console.log(chalk.gray(`       其他文件类型，默认有效`));
    }

    return true;
  }

  /**
   * 生成重试上下文信息
   */
  generateRetryContext(attemptNumber, previousAttempts) {
    if (attemptNumber === 1 || previousAttempts.length === 0) {
      return '';
    }

    let retryContext = `\n\n**🔄 重试信息 (第 ${attemptNumber} 次尝试)**：\n`;
    retryContext += `之前已经尝试了 ${previousAttempts.length} 次修复，但都没有成功。请仔细分析之前的失败原因，采用不同的修复策略。\n\n`;

    // 分析之前的尝试
    if (previousAttempts.length > 0) {
      retryContext += '**之前尝试的问题分析**：\n';

      previousAttempts.forEach((attempt, index) => {
        retryContext += `- 尝试 ${index + 1}: ${attempt.error || '修复失败'}\n`;

        if (attempt.approach) {
          retryContext += `  采用方法: ${attempt.approach}\n`;
        }
      });

      retryContext += '\n**本次修复策略建议**：\n';

      if (attemptNumber === 2) {
        retryContext += '- 🎯 **更激进的修复**: 不要只修复明显的错误，考虑重构整个文件结构\n';
        retryContext += '- 🔍 **深入分析**: 检查依赖关系、导入路径、配置兼容性\n';
        retryContext += '- 📝 **完整重写**: 如果小修小补不行，考虑按照 Vue 3 最佳实践重写\n';
      } else if (attemptNumber === 3) {
        retryContext += '- 🚀 **彻底重构**: 完全按照 Vue 3 + Element Plus 的标准重写文件\n';
        retryContext += '- 🎨 **现代化语法**: 使用最新的 ES6+、Composition API、TypeScript 语法\n';
        retryContext += '- 🔧 **工具链适配**: 确保与最新的构建工具和依赖版本兼容\n';
      } else {
        retryContext += '- 💡 **创新方案**: 尝试完全不同的实现方式\n';
        retryContext += '- 🔬 **细致检查**: 逐行检查每个语法细节\n';
        retryContext += '- 📚 **参考最佳实践**: 查阅官方文档和社区最佳实践\n';
      }
    }

    return retryContext;
  }

  /**
   * 获取之前的尝试记录
   */
  getPreviousAttempts(filesToFix, currentAttemptNumber) {
    const previousAttempts = [];

    // 从历史记录中获取之前的尝试
    for (let i = 1; i < currentAttemptNumber; i++) {
      const attemptKey = `attempt_${i}`;
      if (this.attemptHistory[attemptKey]) {
        this.attemptHistory[attemptKey].forEach(record => {
          if (filesToFix.includes(record.filePath)) {
            previousAttempts.push({
              attemptNumber: i,
              filePath: record.filePath,
              error: record.error,
              approach: record.approach || this.inferApproachFromError(record.error)
            });
          }
        });
      }
    }

    return previousAttempts;
  }

  /**
   * 记录失败的尝试
   */
  recordFailedAttempt(filePath, attemptNumber, error) {
    const attemptKey = `attempt_${attemptNumber}`;

    if (!this.attemptHistory[attemptKey]) {
      this.attemptHistory[attemptKey] = [];
    }

    this.attemptHistory[attemptKey].push({
      filePath,
      error,
      timestamp: new Date().toISOString(),
      approach: this.inferApproachFromError(error)
    });
  }

  /**
   * 从错误信息推断修复方法
   */
  inferApproachFromError(error) {
    if (!error || typeof error !== 'string') {
      return '未知方法';
    }

    const lowerError = error.toLowerCase();

    if (lowerError.includes('内容与原文件相同')) {
      return '小幅修改';
    } else if (lowerError.includes('缺少必要的文件结构')) {
      return '结构修复';
    } else if (lowerError.includes('内容过短')) {
      return '内容补充';
    } else if (lowerError.includes('语法错误') || lowerError.includes('syntax error')) {
      return '语法修复';
    } else if (lowerError.includes('导入') || lowerError.includes('import')) {
      return '导入修复';
    } else {
      return '通用修复';
    }
  }

  /**
   * 显示 AI 响应摘要
   */
  displayAIResponseSummary(response, filePath, attemptNumber) {
    if (!response || typeof response !== 'string') {
      console.log(chalk.yellow('  ⚠️  AI 响应为空或无效'));
      return;
    }

    const fileName = path.basename(filePath);
    const responseLength = response.length;

    console.log(chalk.cyan(`  📄 AI 响应摘要 (${fileName}, 尝试 ${attemptNumber}):`));
    console.log(chalk.gray(`     响应长度: ${responseLength} 字符`));

    // 检查响应中是否包含 XML 标签
    const hasXMLTags = response.includes('<file_fix>') && response.includes('</file_fix>');
    const hasCodeBlocks = response.includes('```');

    if (hasXMLTags) {
      console.log(chalk.green('     ✅ 包含 XML 格式的修复内容'));

      // 提取并显示修复说明
      const explanationMatch = response.match(/<file_fix>([\s\S]*?)<content>/);
      if (explanationMatch) {
        const explanation = explanationMatch[1].trim();
        if (explanation && explanation.length > 10) {
          const shortExplanation = explanation.length > 100
            ? explanation.substring(0, 100) + '...'
            : explanation;
          console.log(chalk.gray(`     说明: ${shortExplanation}`));
        }
      }
    } else if (hasCodeBlocks) {
      console.log(chalk.yellow('     ⚠️  包含代码块格式（非标准 XML）'));
    } else {
      console.log(chalk.red('     ❌ 未检测到有效的修复格式'));
    }

    // 显示响应的前几行作为预览
    const lines = response.split('\n').slice(0, 3);
    const preview = lines.join('\n').substring(0, 150);
    if (preview.trim()) {
      console.log(chalk.gray(`     预览: ${preview.trim()}${response.length > 150 ? '...' : ''}`));
    }
  }

  /**
   * 生成修复会话摘要
   */
  async generateSessionSummary() {
    try {
      const sessionId = new Date().toISOString().replace(/[:.]/g, '-').split('T')[0];
      const summaryPath = path.join(this.options.logDir, `session-summary-${sessionId}.json`);
      
      // 读取所有相关的日志文件
      const logFiles = await this.getSessionLogFiles();
      const sessionData = {
        sessionId: sessionId,
        projectPath: this.projectPath,
        startTime: new Date().toISOString(),
        endTime: new Date().toISOString(),
        totalAttempts: this.fixStats.attempts,
        filesAnalyzed: this.fixStats.filesAnalyzed,
        filesModified: this.fixStats.filesModified,
        attempts: []
      };

      // 按轮次组织日志数据
      for (const logFile of logFiles) {
        try {
          const logData = await fs.readJson(logFile);
          const attemptNumber = logData.context?.attemptNumber || 1;
          
          if (!sessionData.attempts[attemptNumber - 1]) {
            sessionData.attempts[attemptNumber - 1] = {
              attemptNumber: attemptNumber,
              phases: []
            };
          }
          
          sessionData.attempts[attemptNumber - 1].phases.push({
            phase: logData.context?.phase || 'unknown',
            taskType: logData.context?.taskType || 'unknown',
            fileName: logData.context?.fileName || 'unknown',
            success: logData.success,
            duration_ms: logData.duration_ms,
            timestamp: logData.timestamp,
            logFile: path.basename(logFile)
          });
        } catch (error) {
          console.warn(chalk.yellow(`⚠️  无法读取日志文件: ${logFile}`));
        }
      }

      // 写入会话摘要
      await fs.writeJson(summaryPath, sessionData, { spaces: 2 });
      console.log(chalk.blue(`📊 会话摘要已生成: ${path.basename(summaryPath)}`));
      
      return summaryPath;
    } catch (error) {
      console.warn(chalk.yellow(`⚠️  生成会话摘要失败: ${error.message}`));
      return null;
    }
  }

  /**
   * 获取会话相关的日志文件
   */
  async getSessionLogFiles() {
    try {
      // 确保日志目录存在
      await fs.ensureDir(this.options.logDir);

      const files = await fs.readdir(this.options.logDir);
      const logFiles = files
        .filter(file => file.endsWith('.json') && file.includes('ai-call'))
        .map(file => path.join(this.options.logDir, file))
        .sort();

      console.log(chalk.gray(`📁 找到 ${logFiles.length} 个 AI 调用日志文件`));

      // 显示最新的几个日志文件
      if (logFiles.length > 0) {
        const recentFiles = logFiles.slice(-3);
        console.log(chalk.gray('   最新日志文件:'));
        recentFiles.forEach(file => {
          console.log(chalk.gray(`   - ${path.basename(file)}`));
        });
      }

      return logFiles;
    } catch (error) {
      console.warn(chalk.yellow(`⚠️  无法读取日志目录 ${this.options.logDir}: ${error.message}`));

      // 尝试创建日志目录
      try {
        await fs.ensureDir(this.options.logDir);
        console.log(chalk.green(`✅ 已创建日志目录: ${this.options.logDir}`));
        return [];
      } catch (createError) {
        console.error(chalk.red(`❌ 无法创建日志目录: ${createError.message}`));
        return [];
      }
    }
  }

  /**
   * 获取修复统计信息
   */
  getFixStats() {
    return {
      ...this.fixStats,
      aiStats: this.getStats()
    };
  }

  /**
   * 重置修复统计
   */
  resetFixStats() {
    this.fixStats = {
      filesAnalyzed: 0,
      filesModified: 0,
      errorsFixed: 0,
      attempts: 0
    };
    this.resetStats();
  }

  /**
   * 列出所有轮次的日志文件
   */
  async listSessionLogs() {
    try {
      const files = await fs.readdir(this.options.logDir);
      const logFiles = files
        .filter(file => file.endsWith('.json') && file.includes('ai-call'))
        .sort();
      
      if (logFiles.length === 0) {
        console.log(chalk.gray('📝 没有找到 AI 调用日志文件'));
        return [];
      }

      console.log(chalk.blue(`📝 找到 ${logFiles.length} 个 AI 调用日志文件:`));
      
      // 按轮次分组显示
      const attempts = {};
      for (const file of logFiles) {
        const match = file.match(/attempt(\d+)/);
        if (match) {
          const attemptNum = parseInt(match[1]);
          if (!attempts[attemptNum]) {
            attempts[attemptNum] = [];
          }
          attempts[attemptNum].push(file);
        }
      }

      // 显示每个轮次的日志
      Object.keys(attempts).sort((a, b) => parseInt(a) - parseInt(b)).forEach(attemptNum => {
        console.log(chalk.gray(`\n  轮次 ${attemptNum}:`));
        attempts[attemptNum].forEach(file => {
          console.log(chalk.gray(`    - ${file}`));
        });
      });

      return logFiles;
    } catch (error) {
      console.warn(chalk.yellow(`⚠️  无法列出日志文件: ${error.message}`));
      return [];
    }
  }
}

module.exports = BuildFixAgent; 