const PackageUpgrader = require('../src/dependency/packageUpgrader')
const path = require('path')

async function runDependencyUpgrade() {
  console.log('🚀 开始依赖升级示例...\n')

  // 创建 PackageUpgrader 实例
  const upgrader = new PackageUpgrader('./test-project', {
    autoFixConfig: true, // 自动修复配置文件
    preserveVue3Dependencies: true, // 保留已有的 Vue 3 依赖
    verbose: true // 显示详细信息
  })

  try {
    // 执行升级
    const result = await upgrader.upgrade()

    console.log('\n📊 升级结果:')
    console.log(`- 升级的依赖: ${result.upgraded.length} 个`)
    console.log(`- 新增的依赖: ${result.added.length} 个`)
    console.log(`- 删除的依赖: ${result.removed.length} 个`)
    console.log(`- 兼容性问题: ${result.compatibilityIssues?.length || 0} 个`)

    if (result.compatibilityIssues && result.compatibilityIssues.length > 0) {
      console.log('\n🔧 已处理的兼容性问题:')
      result.compatibilityIssues.forEach(issue => {
        console.log(`- ${issue.package}: ${issue.error}`)
        console.log(`  解决方案: ${issue.solution}`)
      })
    }

    console.log('\n✅ 依赖升级完成！')
    console.log('💡 建议运行 "npm install" 或 "pnpm install" 来安装更新后的依赖')

  } catch (error) {
    console.error('❌ 升级失败:', error.message)
    process.exit(1)
  }
}

// 如果直接运行此文件
if (require.main === module) {
  runDependencyUpgrade()
}

module.exports = { runDependencyUpgrade } 