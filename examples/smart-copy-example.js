const path = require('path');
const AutoMigrator = require('../src/AutoMigrator');

/**
 * 智能文件复制示例
 * 
 * 这个示例展示了如何使用 AutoMigrator 的智能文件复制功能。
 * 
 * 智能复制功能会：
 * 1. 检查是否有 AI 能力
 * 2. 如果有 AI，使用 AI 分析项目结构，确定需要复制的文件
 * 3. 如果没有 AI，使用默认策略复制常见的必要文件（如 mock、public、配置文件等）
 */

async function runSmartCopyExample() {
  console.log('🧠 智能文件复制示例\n');

  // 示例1: 使用 AI 进行智能复制（需要设置 AI API Key）
  console.log('示例1: 使用 AI 进行智能复制');
  console.log('================================');

  try {
    const sourceProjectPath = './test-project';
    const targetProjectPath = './migrated-project-with-ai';

    const migratorWithAI = new AutoMigrator(sourceProjectPath, {
      sourceToTargetMode: true,
      sourceProjectPath: sourceProjectPath,
      targetProjectPath: targetProjectPath,
      aiApiKey: process.env.DEEPSEEK_API_KEY || process.env.GLM_API_KEY || process.env.OPENAI_API_KEY,
      verbose: true
    });

    console.log('正在使用 AI 分析项目结构并复制文件...');
    // 注意：这里只是演示智能复制功能，实际使用中应该调用完整的 migrate() 方法
    await migratorWithAI.smartCopyAdditionalFiles();
    console.log('✅ AI 驱动的智能复制完成\n');

  } catch (error) {
    console.log('⚠️  AI 智能复制失败，可能是因为缺少 AI API Key');
    console.log(`错误信息: ${error.message}\n`);
  }

  // 示例2: 使用默认策略进行复制（不需要 AI）
  console.log('示例2: 使用默认策略进行复制');
  console.log('================================');

  try {
    const sourceProjectPath = './test-project';
    const targetProjectPath = './migrated-project-default';

    const migratorDefault = new AutoMigrator(sourceProjectPath, {
      sourceToTargetMode: true,
      sourceProjectPath: sourceProjectPath,
      targetProjectPath: targetProjectPath,
      // 不设置 aiApiKey，将使用默认策略
      verbose: true
    });

    console.log('正在使用默认策略复制文件...');
    await migratorDefault.smartCopyAdditionalFiles();
    console.log('✅ 默认策略复制完成');

    console.log('\n默认策略会复制以下文件和目录:');
    console.log('📁 配置文件: vue.config.js, babel.config.js, .eslintrc.js, tsconfig.json 等');
    console.log('📁 环境文件: .env, .env.development, .env.production 等');
    console.log('📁 目录: mock/, public/, static/, assets/, scripts/, tests/ 等');

  } catch (error) {
    console.log('❌ 默认策略复制失败');
    console.log(`错误信息: ${error.message}`);
  }

  // 使用建议
  console.log('\n💡 使用建议');
  console.log('===========');
  console.log('1. 设置 AI API Key 环境变量以启用 AI 智能分析:');
  console.log('   export DEEPSEEK_API_KEY=your_key');
  console.log('   export GLM_API_KEY=your_key');
  console.log('   export OPENAI_API_KEY=your_key');
  console.log('');
  console.log('2. AI 模式会分析项目的具体结构，识别真正需要的文件');
  console.log('3. 默认模式会复制常见的必要文件，适合大多数项目');
  console.log('4. 智能复制会排除不必要的文件（node_modules、dist、.git 等）');
  console.log('5. 可以通过 verbose: true 选项查看详细的复制过程');
}

// 如果直接运行此文件，执行示例
if (require.main === module) {
  runSmartCopyExample().catch(console.error);
}

module.exports = { runSmartCopyExample }; 